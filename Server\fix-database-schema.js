// VMS Production Database Schema Fix
// Fixes critical database issues for production deployment

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production',
  multipleStatements: true
};

async function fixDatabaseSchema() {
  let connection;
  
  try {
    console.log('🔧 Connecting to VMS Production Database...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ Connected to database successfully');
    
    // Fix 1: Add missing user_name column to audit_logs table
    console.log('🔧 Adding user_name column to audit_logs...');
    try {
      await connection.execute(`
        ALTER TABLE audit_logs 
        ADD COLUMN user_name VARCHAR(255) DEFAULT NULL 
        COMMENT 'Username for audit trail'
      `);
      console.log('✅ Added user_name column to audit_logs');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  user_name column already exists in audit_logs');
      } else {
        throw error;
      }
    }
    
    // Fix 2: Add missing last_selected_year column to users table
    console.log('🔧 Adding last_selected_year column to users...');
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN last_selected_year INT DEFAULT NULL 
        COMMENT 'Last selected financial year by user'
      `);
      console.log('✅ Added last_selected_year column to users');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  last_selected_year column already exists in users');
      } else {
        throw error;
      }
    }
    
    // Fix 3: Create indexes for better performance
    console.log('🔧 Creating performance indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_department ON audit_logs(department)',
      'CREATE INDEX IF NOT EXISTS idx_users_last_selected_year ON users(last_selected_year)'
    ];
    
    for (const indexQuery of indexes) {
      try {
        await connection.execute(indexQuery);
        console.log(`✅ Created index: ${indexQuery.split(' ')[5]}`);
      } catch (error) {
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log(`ℹ️  Index already exists: ${indexQuery.split(' ')[5]}`);
        } else {
          console.log(`⚠️  Index creation warning: ${error.message}`);
        }
      }
    }
    
    // Fix 4: Verify table structures
    console.log('🔍 Verifying table structures...');
    
    const [auditCols] = await connection.execute('DESCRIBE audit_logs');
    const [userCols] = await connection.execute('DESCRIBE users');
    
    const auditColNames = auditCols.map(col => col.Field);
    const userColNames = userCols.map(col => col.Field);
    
    console.log('📋 audit_logs columns:', auditColNames.length, 'columns');
    console.log('📋 users columns:', userColNames.length, 'columns');
    
    // Check if critical columns exist
    const hasUserName = auditColNames.includes('user_name');
    const hasLastSelectedYear = userColNames.includes('last_selected_year');
    
    console.log('✅ audit_logs.user_name:', hasUserName ? 'EXISTS' : 'MISSING');
    console.log('✅ users.last_selected_year:', hasLastSelectedYear ? 'EXISTS' : 'MISSING');
    
    // Fix 5: Clean up any orphaned data
    console.log('🧹 Cleaning up orphaned audit log entries...');
    const [result] = await connection.execute(`
      DELETE FROM audit_logs 
      WHERE user_id IS NOT NULL 
      AND user_id != '' 
      AND user_id NOT IN (SELECT id FROM users)
    `);
    
    console.log(`🗑️  Cleaned up ${result.affectedRows} orphaned audit log entries`);
    
    console.log('');
    console.log('🎉 DATABASE SCHEMA FIXES COMPLETED SUCCESSFULLY!');
    console.log('✅ All missing columns have been added');
    console.log('✅ Performance indexes have been created');
    console.log('✅ Orphaned data has been cleaned up');
    console.log('✅ VMS system is now ready for production deployment!');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the fix
fixDatabaseSchema();
