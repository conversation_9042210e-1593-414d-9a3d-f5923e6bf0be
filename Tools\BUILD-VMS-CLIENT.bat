@echo off
title Building Modern VMS Client (.NET 9.0)
color 0A

echo ================================================================
echo                  BUILD MODERN VMS CLIENT
echo                    .NET 9.0 - PRODUCTION READY
echo                   SELF-CONTAINED DEPLOYMENT
echo ================================================================
echo.

echo [INFO] 🚀 Building modern VMS Client with .NET 9.0...
echo [INFO] 📦 Self-contained - NO .NET required on client PCs
echo.

REM Install dependencies
echo [2/4] 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Failed to install dependencies!
    pause
    exit /b 1
)

echo [SUCCESS] ✅ Dependencies installed
echo.

REM Install pkg globally if not already installed
echo [3/4] 🔧 Installing pkg (executable builder)...
call npm install -g pkg
if %errorlevel% neq 0 (
    echo [WARNING] ⚠️ Failed to install pkg globally, trying local install...
    call npm install pkg
)

echo [SUCCESS] ✅ pkg installed
echo.

REM Build the executable
echo [4/4] 🏗️ Building VMS-Client.exe...
call npx pkg vms-client.js --targets node18-win-x64 --output VMS-Client.exe
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Failed to build executable!
    pause
    exit /b 1
)

echo.
echo ================================================================
echo                  BUILD COMPLETED SUCCESSFULLY
echo ================================================================
echo.

if exist "VMS-Client.exe" (
    echo ✅ SUCCESS: VMS-Client.exe has been created!
    echo.
    echo 📁 Location: %CD%\VMS-Client.exe
    echo 📏 Size: 
    for %%A in ("VMS-Client.exe") do echo    %%~zA bytes
    echo.
    echo 🚀 DEPLOYMENT INSTRUCTIONS:
    echo   1. Copy VMS-Client.exe to user computers
    echo   2. Users double-click VMS-Client.exe
    echo   3. Client automatically finds VMS server
    echo   4. Chrome opens with VMS ready to use
    echo.
    echo 💡 FEATURES:
    echo   ✅ Automatic server discovery
    echo   ✅ No IP address typing needed
    echo   ✅ Opens Chrome automatically
    echo   ✅ Works on any Windows computer
    echo   ✅ No installation required
    echo.
    echo Press any key to test the client...
    pause >nul
    
    echo.
    echo [TESTING] 🧪 Running VMS-Client.exe...
    start "" "VMS-Client.exe"
    
) else (
    echo [ERROR] ❌ VMS-Client.exe was not created!
    echo [INFO] Check the error messages above for details.
)

echo.
echo Press any key to close...
pause >nul
