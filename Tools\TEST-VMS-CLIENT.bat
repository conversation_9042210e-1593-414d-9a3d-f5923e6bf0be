@echo off
title Testing VMS Client
color 0B

echo ================================================================
echo                    TEST VMS CLIENT
echo              Verify Client Functionality
echo ================================================================
echo.

echo [INFO] 🧪 Testing VMS Client functionality...
echo.

REM Test 1: Check if Node.js client works
echo [TEST 1] 📝 Testing Node.js version...
if exist "vms-client.js" (
    echo [SUCCESS] ✅ vms-client.js found
    
    echo [RUNNING] 🚀 Testing Node.js client...
    timeout /t 2 /nobreak >nul
    node vms-client.js
    
) else (
    echo [ERROR] ❌ vms-client.js not found!
)

echo.
echo [TEST 2] 📦 Testing executable version...
if exist "VMS-Client.exe" (
    echo [SUCCESS] ✅ VMS-Client.exe found
    
    echo [INFO] File size:
    for %%A in ("VMS-Client.exe") do echo   %%~zA bytes
    
    echo.
    echo [RUNNING] 🚀 Testing executable client...
    echo [INFO] This will open Chrome if VMS server is running...
    timeout /t 3 /nobreak >nul
    
    start "" "VMS-Client.exe"
    
) else (
    echo [WARNING] ⚠️ VMS-Client.exe not found!
    echo [INFO] Run BUILD-VMS-CLIENT.bat to create the executable
)

echo.
echo ================================================================
echo                    TEST COMPLETED
echo ================================================================
echo.
echo [INFO] If Chrome opened with VMS, the client is working correctly!
echo [INFO] If not, check that VMS server is running on the network.
echo.
echo Press any key to close...
pause >nul
