@echo off
title Building VMS Client GUI
color 0A

echo ================================================================
echo                  BUILD VMS CLIENT GUI
echo              Beautiful Windows Application
echo                   NO NODE.JS REQUIRED
echo ================================================================
echo.

echo [INFO] 🎨 Building beautiful VMS Client with GUI...
echo.

REM Check if .NET Framework is available
echo [1/4] 🔍 Checking .NET Framework...

REM Try to find .NET Framework compiler
set "CSC_PATH="
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set "CSC_PATH=%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo [SUCCESS] ✅ .NET Framework 4.0+ found (64-bit)
) else if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set "CSC_PATH=%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo [SUCCESS] ✅ .NET Framework 4.0+ found (32-bit)
) else if exist "%ProgramFiles%\dotnet\dotnet.exe" (
    echo [INFO] 📦 .NET Core/5+ found, using dotnet build...
    goto :dotnet_build
) else (
    echo [ERROR] ❌ .NET Framework not found!
    echo [INFO] Please install .NET Framework 4.0 or later
    echo [INFO] Download from: https://dotnet.microsoft.com/download/dotnet-framework
    pause
    exit /b 1
)

echo.
echo [2/4] 📦 Preparing build environment...

REM Create temporary build directory
if not exist "build" mkdir build
cd build

REM Copy source file
copy "..\VMS-Client-GUI.cs" "VMS-Client-GUI.cs" >nul

echo [SUCCESS] ✅ Build environment ready
echo.

echo [3/4] 🏗️ Compiling VMS Client GUI...

REM Compile the C# application
"%CSC_PATH%" /target:winexe /out:VMS-Client.exe /reference:System.dll /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /reference:System.Net.Http.dll VMS-Client-GUI.cs

if %errorlevel% neq 0 (
    echo [ERROR] ❌ Compilation failed!
    echo [INFO] Check the error messages above for details.
    cd ..
    pause
    exit /b 1
)

echo [SUCCESS] ✅ Compilation completed
echo.

echo [4/4] 📁 Finalizing build...

REM Move executable to Tools directory
move "VMS-Client.exe" "..\VMS-Client-GUI.exe" >nul

REM Clean up build directory
cd ..
rmdir /s /q build >nul 2>&1

goto :build_complete

:dotnet_build
echo [INFO] 📦 Using .NET Core/5+ build process...

REM Create a simple project file for .NET Core
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > VMS-Client.csproj
echo   ^<PropertyGroup^> >> VMS-Client.csproj
echo     ^<OutputType^>WinExe^</OutputType^> >> VMS-Client.csproj
echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^> >> VMS-Client.csproj
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> VMS-Client.csproj
echo     ^<AssemblyName^>VMS-Client-GUI^</AssemblyName^> >> VMS-Client.csproj
echo   ^</PropertyGroup^> >> VMS-Client.csproj
echo ^</Project^> >> VMS-Client.csproj

REM Rename source file for .NET Core
ren "VMS-Client-GUI.cs" "Program.cs"

REM Build with dotnet
dotnet build -c Release -o output

if %errorlevel% neq 0 (
    echo [ERROR] ❌ .NET Core build failed!
    pause
    exit /b 1
)

REM Move executable
move "output\VMS-Client-GUI.exe" "VMS-Client-GUI.exe" >nul

REM Clean up
del "VMS-Client.csproj" >nul 2>&1
ren "Program.cs" "VMS-Client-GUI.cs"
rmdir /s /q output >nul 2>&1

:build_complete

echo ================================================================
echo                  BUILD COMPLETED SUCCESSFULLY
echo ================================================================
echo.

if exist "VMS-Client-GUI.exe" (
    echo ✅ SUCCESS: VMS-Client-GUI.exe has been created!
    echo.
    echo 📁 Location: %CD%\VMS-Client-GUI.exe
    echo 📏 Size: 
    for %%A in ("VMS-Client-GUI.exe") do echo    %%~zA bytes
    echo.
    echo 🎨 FEATURES:
    echo   ✅ Beautiful Windows GUI
    echo   ✅ Spinning loader animation
    echo   ✅ Professional user interface
    echo   ✅ No technical information shown
    echo   ✅ Automatic server discovery
    echo   ✅ Opens Chrome automatically
    echo   ✅ No Node.js required
    echo   ✅ Works on any Windows computer
    echo.
    echo 🚀 DEPLOYMENT INSTRUCTIONS:
    echo   1. Copy VMS-Client-GUI.exe to user computers
    echo   2. Users double-click the executable
    echo   3. Beautiful GUI shows "Please wait while we connect you to the VMS SYSTEM"
    echo   4. Client finds server automatically (behind the scenes)
    echo   5. Chrome opens with VMS ready to use
    echo.
    echo 💡 USER EXPERIENCE:
    echo   - Clean, professional interface
    echo   - Spinning animation during connection
    echo   - No technical details or IP addresses shown
    echo   - Simple "Try Again" button if connection fails
    echo   - Automatic browser launch
    echo.
    echo Press any key to test the GUI client...
    pause >nul
    
    echo.
    echo [TESTING] 🧪 Running VMS-Client-GUI.exe...
    start "" "VMS-Client-GUI.exe"
    
) else (
    echo [ERROR] ❌ VMS-Client-GUI.exe was not created!
    echo [INFO] Check the error messages above for details.
)

echo.
echo Press any key to close...
pause >nul
