#!/usr/bin/env node

/**
 * VMS Client Launcher
 * Automatically discovers VMS server and opens Chrome browser
 * Can be compiled to .exe using pkg: npm install -g pkg && pkg vms-client.js
 */

const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const os = require('os');
const path = require('path');

const execAsync = promisify(exec);

class VMSClient {
  constructor() {
    this.chromePaths = this.getChromePaths();
    this.testUrls = [];
    this.serverUrl = null;
  }

  /**
   * Get possible Chrome installation paths
   */
  getChromePaths() {
    const platform = os.platform();
    
    if (platform === 'win32') {
      return [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
        path.join(os.homedir(), 'AppData\\Local\\Chromium\\Application\\chrome.exe')
      ];
    } else if (platform === 'darwin') {
      return [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Chromium.app/Contents/MacOS/Chromium'
      ];
    } else {
      return [
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser',
        '/usr/bin/chromium',
        '/snap/bin/chromium'
      ];
    }
  }

  /**
   * Find Chrome browser installation
   */
  async findChrome() {
    console.log('🌐 Locating Chrome browser...');
    
    for (const chromePath of this.chromePaths) {
      try {
        const fs = require('fs');
        if (fs.existsSync(chromePath)) {
          console.log('✅ Chrome found:', chromePath);
          return chromePath;
        }
      } catch (error) {
        // Continue to next path
      }
    }
    
    throw new Error('Chrome browser not found. Please install Google Chrome.');
  }

  /**
   * Generate test URLs for server discovery
   */
  generateTestUrls() {
    const urls = [
      'http://localhost:8080',
      'http://127.0.0.1:8080'
    ];

    // Get network interfaces
    const interfaces = os.networkInterfaces();
    
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          const ip = iface.address;
          const parts = ip.split('.');
          const network = `${parts[0]}.${parts[1]}.${parts[2]}`;
          
          // Add common server IPs in the network
          urls.push(`http://${network}.1:8080`);
          urls.push(`http://${network}.100:8080`);
          urls.push(`http://${network}.101:8080`);
          urls.push(`http://${network}.102:8080`);
          urls.push(`http://${network}.200:8080`);
        }
      }
    }

    return [...new Set(urls)]; // Remove duplicates
  }

  /**
   * Test if VMS server is running at given URL
   */
  async testServer(url) {
    try {
      const response = await fetch(`${url}/health`, {
        method: 'GET',
        timeout: 3000,
        signal: AbortSignal.timeout(3000)
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.status === 'healthy' || data.message) {
          return true;
        }
      }
    } catch (error) {
      // Server not responding
    }
    return false;
  }

  /**
   * Discover VMS server on network
   */
  async discoverServer() {
    console.log('🔍 Discovering VMS Server on network...');
    
    this.testUrls = this.generateTestUrls();
    
    for (const url of this.testUrls) {
      console.log(`🔗 Testing ${url}...`);
      
      if (await this.testServer(url)) {
        console.log(`✅ VMS Server found at ${url}`);
        this.serverUrl = url;
        return url;
      }
    }
    
    throw new Error('VMS Server not found on network');
  }

  /**
   * Launch Chrome with VMS URL
   */
  async launchChrome(chromePath, url) {
    console.log('🚀 Launching VMS in Chrome...');
    console.log(`🌐 Opening: ${url}`);
    
    const args = [
      '--new-window',
      '--start-maximized',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      url
    ];

    return new Promise((resolve, reject) => {
      const chrome = spawn(chromePath, args, {
        detached: true,
        stdio: 'ignore'
      });

      chrome.unref();
      
      setTimeout(() => {
        console.log('✅ SUCCESS: VMS is now open in Chrome browser');
        resolve();
      }, 2000);
    });
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      console.log('================================================================');
      console.log('                    VMS CLIENT LAUNCHER');
      console.log('                 Automatic Server Discovery');
      console.log('================================================================');
      console.log('');
      console.log('🔍 Searching for VMS Server on network...');
      console.log('⏳ Please wait while we connect you to VMS...');
      console.log('');

      // Step 1: Find Chrome
      const chromePath = await this.findChrome();
      
      // Step 2: Discover VMS Server
      const serverUrl = await this.discoverServer();
      
      // Step 3: Launch Chrome
      await this.launchChrome(chromePath, serverUrl);
      
      console.log('');
      console.log('================================================================');
      console.log('                    VMS CLIENT CONNECTED');
      console.log('================================================================');
      console.log('');
      console.log(`✅ SUCCESS: VMS is now open in Chrome browser`);
      console.log(`🌐 Server: ${serverUrl}`);
      console.log(`🖥️  Browser: Google Chrome`);
      console.log('');
      console.log('You can now close this window and use VMS normally.');
      console.log('If you need to reconnect, just run VMS Client again.');
      
    } catch (error) {
      console.error('');
      console.error('❌ ERROR:', error.message);
      console.error('');
      console.error('🔧 TROUBLESHOOTING:');
      console.error('  1. Make sure VMS Server is running');
      console.error('  2. Check you are connected to the same network');
      console.error('  3. Verify firewall is not blocking connections');
      console.error('  4. Install Google Chrome if not already installed');
      console.error('');
      console.error('🖱️  MANUAL ACCESS:');
      console.error('  You can manually open Chrome and go to:');
      console.error('  - http://localhost:8080 (if server is on this computer)');
      console.error('  - http://[SERVER-IP]:8080 (replace with actual server IP)');
      
      process.exit(1);
    }
  }
}

// Add fetch polyfill for older Node.js versions
if (!global.fetch) {
  global.fetch = require('node-fetch');
}

// Run the VMS Client
if (require.main === module) {
  const client = new VMSClient();
  client.run();
}

module.exports = VMSClient;
