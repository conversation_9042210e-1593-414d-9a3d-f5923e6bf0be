{"level":"error","message":"listen EADDRINUSE: address already in use 0.0.0.0:8080","pid":22004,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-08-02T22:53:21.279Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-08-02 22:53:26"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 22:54:36"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch 3d26e6d9-9b6a-47c0-b67d-ead4d6ede804","service":"vms-server","timestamp":"2025-08-02 22:56:01"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 22:56:48"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch fe21ffb8-84f3-4a33-9e91-38d4495012d0","service":"vms-server","timestamp":"2025-08-02 22:58:01"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 23:03:52"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 16:00:26"}
{"level":"error","message":"🔍 VOUCHER UPDATE DEBUG: No valid updates for voucher f622a8e4-0fc1-4719-b39c-e8bd162b840d. Received fields: isOnHold, is_on_hold, holdComment, hold_comment","service":"vms-server","timestamp":"2025-08-03 16:01:27"}
