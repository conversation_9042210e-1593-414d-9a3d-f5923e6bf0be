# Create VMS Icon - Convert JPG to proper ICO format
Write-Host "Creating VMS Icon from your logo..." -ForegroundColor Green

# Check if logo exists
if (-not (Test-Path "vms logo.jpg")) {
    Write-Host "Error: vms logo.jpg not found!" -ForegroundColor Red
    exit 1
}

# Load required assemblies
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

try {
    Write-Host "Loading VMS logo..." -ForegroundColor Cyan
    
    # Load the original image
    $originalImage = [System.Drawing.Image]::FromFile((Get-Item "vms logo.jpg").FullName)
    
    # Create multiple icon sizes (Windows standard)
    $iconSizes = @(16, 24, 32, 48, 64, 128, 256)
    $iconImages = @()
    
    foreach ($size in $iconSizes) {
        Write-Host "Creating ${size}x${size} icon..." -ForegroundColor Yellow
        
        # Create bitmap with high quality
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        # Draw the resized image
        $graphics.DrawImage($originalImage, 0, 0, $size, $size)
        
        # Convert to icon
        $hIcon = $bitmap.GetHicon()
        $icon = [System.Drawing.Icon]::FromHandle($hIcon)
        
        $iconImages += $icon
        
        # Clean up
        $graphics.Dispose()
        $bitmap.Dispose()
    }
    
    Write-Host "Saving VMS icon..." -ForegroundColor Cyan
    
    # Save the first (32x32) icon as the main icon file
    $iconStream = New-Object System.IO.FileStream("vms-logo.ico", [System.IO.FileMode]::Create)
    $iconImages[2].Save($iconStream)  # Use 32x32 size
    $iconStream.Close()
    
    # Clean up
    foreach ($icon in $iconImages) {
        $icon.Dispose()
    }
    $originalImage.Dispose()
    
    Write-Host "✅ VMS icon created successfully!" -ForegroundColor Green
    Write-Host "📁 File: vms-logo.ico" -ForegroundColor Cyan
    
    # Verify the file was created
    if (Test-Path "vms-logo.ico") {
        $iconFile = Get-Item "vms-logo.ico"
        Write-Host "📏 Size: $($iconFile.Length) bytes" -ForegroundColor Cyan
        Write-Host "🎯 Ready to use in VMS Client!" -ForegroundColor Green
    } else {
        Write-Host "❌ Icon file was not created properly" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Error creating icon: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
