# Create Simple VMS Icon
Write-Host "Creating simple VMS icon..." -ForegroundColor Green

Add-Type -AssemblyName System.Drawing

try {
    # Load the original image
    $logoPath = (Get-Item "vms logo.jpg").FullName
    $originalImage = [System.Drawing.Image]::FromFile($logoPath)
    
    # Create a 32x32 bitmap
    $size = 32
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set high quality
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    
    # Draw the image
    $graphics.DrawImage($originalImage, 0, 0, $size, $size)
    
    # Save as PNG first (which works better for conversion)
    $bitmap.Save("temp-icon.png", [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Now convert PNG to ICO using .NET method
    $pngImage = [System.Drawing.Image]::FromFile((Get-Item "temp-icon.png").FullName)
    $icon = [System.Drawing.Icon]::FromHandle(([System.Drawing.Bitmap]$pngImage).GetHicon())
    
    # Save as ICO
    $fileStream = [System.IO.File]::Create("vms-logo.ico")
    $icon.Save($fileStream)
    $fileStream.Close()
    
    # Clean up
    $graphics.Dispose()
    $bitmap.Dispose()
    $originalImage.Dispose()
    $pngImage.Dispose()
    $icon.Dispose()
    
    # Remove temp file
    Remove-Item "temp-icon.png" -Force -ErrorAction SilentlyContinue
    
    Write-Host "✅ Simple VMS icon created!" -ForegroundColor Green
    
    if (Test-Path "vms-logo.ico") {
        $iconFile = Get-Item "vms-logo.ico"
        Write-Host "📁 File: vms-logo.ico ($($iconFile.Length) bytes)" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Fallback: Create a basic colored square icon
    Write-Host "Creating fallback icon..." -ForegroundColor Yellow
    
    $bitmap = New-Object System.Drawing.Bitmap(32, 32)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Fill with VMS blue color
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(25, 118, 210))
    $graphics.FillRectangle($brush, 0, 0, 32, 32)
    
    # Add white "VMS" text
    $font = New-Object System.Drawing.Font("Arial", 8, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $graphics.DrawString("VMS", $font, $textBrush, 2, 10)
    
    # Save as ICO
    $icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
    $fileStream = [System.IO.File]::Create("vms-logo.ico")
    $icon.Save($fileStream)
    $fileStream.Close()
    
    # Clean up
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $textBrush.Dispose()
    $font.Dispose()
    $icon.Dispose()
    
    Write-Host "✅ Fallback VMS icon created!" -ForegroundColor Green
}
