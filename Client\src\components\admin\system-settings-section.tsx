import { useState } from 'react';
import { useAppStore } from '@/lib/store/hooks';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FiscalMonth, SystemSettings } from '@/lib/store/types';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { SaveIcon, RefreshCcw } from 'lucide-react';
import { toast } from 'sonner';

export function SystemSettingsSection() {
  const store = useAppStore();
  const { systemSettings } = store;

  const [settings, setSettings] = useState<SystemSettings>({...systemSettings});

  const fiscalMonths: FiscalMonth[] = [
    'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
    'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'
  ];

  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validate settings before saving
  const validateSettings = () => {
    const newErrors: Record<string, string> = {};

    // Validate fiscal year
    if (settings.currentFiscalYear < 2000 || settings.currentFiscalYear > 2100) {
      newErrors.currentFiscalYear = 'Fiscal year must be between 2000 and 2100';
    }

    // Validate session timeout
    if (settings.sessionTimeout < 1 || settings.sessionTimeout > 1440) {
      newErrors.sessionTimeout = 'Session timeout must be between 1 and 1440 minutes';
    }

    // Validate fiscal months order
    const monthOrder = fiscalMonths.indexOf(settings.fiscalYearStart);
    const endMonthOrder = fiscalMonths.indexOf(settings.fiscalYearEnd);
    if (monthOrder === endMonthOrder) {
      newErrors.fiscalYearEnd = 'Fiscal year start and end cannot be the same month';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveSettings = () => {
    if (!validateSettings()) {
      toast.error('Please fix the errors before saving');
      return;
    }

    setIsSaving(true);
    try {
      store.configureSystem(settings);
      toast.success('System settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings({...systemSettings});
    setErrors({});
    toast('Settings reset to current values');
  };

  return (
    <div className="p-6">
      <div className="mb-4">
        <h2 className="text-2xl font-bold">System Settings</h2>
        <p className="text-muted-foreground">Configure system-wide settings and preferences</p>
      </div>

      <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Fiscal Year Configuration</CardTitle>
            <CardDescription>Set your organization's fiscal year start and end months</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fiscalYearStart">Fiscal Year Start</Label>
                <Select
                  value={settings.fiscalYearStart}
                  onValueChange={(value: FiscalMonth) => setSettings({...settings, fiscalYearStart: value})}
                >
                  <SelectTrigger id="fiscalYearStart">
                    <SelectValue placeholder="Select start month" />
                  </SelectTrigger>
                  <SelectContent>
                    {fiscalMonths.map((month) => (
                      <SelectItem key={month} value={month}>{month}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <div className="space-y-1">
                  <Label htmlFor="fiscalYearEnd">Fiscal Year End</Label>
                  <Select
                    value={settings.fiscalYearEnd}
                    onValueChange={(value: FiscalMonth) => setSettings({...settings, fiscalYearEnd: value})}
                  >
                    <SelectTrigger id="fiscalYearEnd" className={errors.fiscalYearEnd ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select end month" />
                    </SelectTrigger>
                    <SelectContent>
                      {fiscalMonths.map((month) => (
                        <SelectItem key={month} value={month}>{month}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.fiscalYearEnd && (
                    <p className="text-xs text-red-500">{errors.fiscalYearEnd}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="space-y-1">
                <Label htmlFor="currentFiscalYear">Current Fiscal Year</Label>
                <Input
                  id="currentFiscalYear"
                  type="number"
                  value={settings.currentFiscalYear}
                  onChange={(e) => setSettings({...settings, currentFiscalYear: parseInt(e.target.value)})}
                  className={errors.currentFiscalYear ? 'border-red-500' : ''}
                />
                {errors.currentFiscalYear && (
                  <p className="text-xs text-red-500">{errors.currentFiscalYear}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Preferences</CardTitle>
            <CardDescription>Configure general system behavior</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="systemTime">System Time</Label>
              <div className="flex gap-2">
                <Input
                  id="systemTime"
                  type="datetime-local"
                  value={settings.systemTime ? new Date(settings.systemTime).toISOString().slice(0, 16) : ''}
                  onChange={(e) => setSettings({...settings, systemTime: new Date(e.target.value).toISOString()})}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setSettings({...settings, systemTime: new Date().toISOString()})}
                  className="whitespace-nowrap"
                >
                  Reset to Live Time
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Override system time for testing purposes. Click "Reset to Live Time" to use server time and enable automatic rollover.
              </p>
            </div>

            <div className="space-y-2">
              <div className="space-y-1">
                <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  min={1}
                  max={1440}
                  value={settings.sessionTimeout}
                  onChange={(e) => setSettings({...settings, sessionTimeout: parseInt(e.target.value)})}
                  className={errors.sessionTimeout ? 'border-red-500' : ''}
                />
                {errors.sessionTimeout ? (
                  <p className="text-xs text-red-500">{errors.sessionTimeout}</p>
                ) : (
                  <p className="text-xs text-muted-foreground">User will be logged out after this period of inactivity</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Switch
                id="autoBackupEnabled"
                checked={settings.autoBackupEnabled}
                onCheckedChange={(checked) => setSettings({...settings, autoBackupEnabled: checked})}
              />
              <Label htmlFor="autoBackupEnabled">Enable Automatic Backup</Label>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-6 flex justify-end space-x-4">
        <Button variant="outline" onClick={handleReset} disabled={isSaving}>
          <RefreshCcw className="mr-2 h-4 w-4" />
          Reset
        </Button>
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          <SaveIcon className="mr-2 h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  );
}