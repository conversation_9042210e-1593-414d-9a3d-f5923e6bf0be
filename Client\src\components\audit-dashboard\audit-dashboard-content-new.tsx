import { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import { useAppStore } from '@/lib/store';
import { Department } from '@/lib/types';
import { NewlyArrivedVouchers } from './newly-arrived-vouchers';
import { DepartmentVoucherHubs } from './department-voucher-hubs';
import { DepartmentVoucherHub } from '@/components/department-voucher-hub';
import { DepartmentProvisionalCash } from '@/components/department-provisional-cash';
import { VoucherBatchReceiving } from '@/components/voucher-batch-receiving';
import { toast } from '@/hooks/use-toast';
import { DispatchControls } from './dispatch-controls';
import { getSocket } from '@/lib/socket';
import { AuditVoucherBatchNotification } from './audit-voucher-batch-notification-new';
import { PerformanceMonitor } from '@/components/performance-monitor';
import { useResourceLock } from '@/hooks/use-resource-lock';

export function AuditDashboardContent() {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);
  // CRITICAL FIX: Use department-specific batch filtering instead of all batches
  const voucherBatches = useAppStore((state) =>
    currentUser ? state.getVoucherBatchesForDepartment(currentUser.department) : []
  );
  const vouchers = useAppStore((state) => state.vouchers);
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const sendVouchersFromAuditToDepartment = useAppStore((state) => state.sendVouchersFromAuditToDepartment);
  const fetchBatches = useAppStore((state) => state.fetchBatches);
  const fetchNotifications = useAppStore((state) => state.fetchNotifications);
  const notifications = useAppStore((state) =>
    currentUser ? state.getNotificationsForUser(currentUser.id) : []
  );

  const [activeBatchId, setActiveBatchId] = useState<string | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [showProvisionalCash, setShowProvisionalCash] = useState(false);
  const [selectedDispatchVouchers, setSelectedDispatchVouchers] = useState<string[]>([]);
  const [dispatchPerson, setDispatchPerson] = useState<string>("SELECT_PERSON");
  const [previousDepartment, setPreviousDepartment] = useState<Department | null>(null);

  // RESOURCE LOCK FIX: Add resource lock for dispatch operations when a department is selected
  const {
    isEditable: isDispatchEditable
  } = useResourceLock(
    'voucher-hub',
    selectedDepartment,
    {
      autoRelease: false // Don't auto-release for dispatch operations
    },
    selectedDepartment || undefined
  );

  // Redirect if not logged in or not in AUDIT department
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== 'AUDIT') {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  // Fetch batches when AUDIT dashboard loads
  useEffect(() => {
    if (currentUser && currentUser.department === 'AUDIT') {
      fetchBatches().catch((error) => {
        console.error('❌ AUDIT Dashboard: Failed to fetch batches:', error);
      });
    }
  }, [currentUser?.id]); // Remove fetchBatches from dependencies to prevent infinite loop

  // REAL-TIME FIX: Listen for new batch notifications
  useEffect(() => {
    if (currentUser?.department === 'AUDIT') {
      const socket = getSocket();
      if (!socket) return;

      const handleNewBatchNotification = (data: any) => {
        console.log('🔔 REAL-TIME: New batch notification received:', data);

        // Show toast notification
        toast({
          title: "New Voucher Batch Arrived",
          description: `${data.message} from ${data.department}`,
          variant: "default",
        });

        // Refresh batches to show new notification
        fetchBatches().catch(console.error);
        fetchNotifications().catch(console.error);
      };

      // NOTIFICATION CONCURRENCY FIX: Handle batch claim notifications
      const handleBatchClaimed = (data: any) => {
        console.log('🔒 REAL-TIME: Batch claimed by another user:', data);

        // Show toast notification if another user claimed the batch
        if (data.claimedById !== currentUser?.id) {
          toast({
            title: "Batch Claimed",
            description: `${data.claimedBy} is now processing this batch`,
            variant: "default",
          });
        }

        // Refresh batches to remove the notification from UI
        fetchBatches().catch(console.error);
        fetchNotifications().catch(console.error);
      };

      // Listen for real-time batch notifications
      socket.on('new_batch_notification', handleNewBatchNotification);
      socket.on('batch_claimed', handleBatchClaimed);

      return () => {
        socket.off('new_batch_notification', handleNewBatchNotification);
        socket.off('batch_claimed', handleBatchClaimed);
      };
    }
  }, [currentUser, fetchBatches, fetchNotifications]);

  // Get unread notifications
  const unreadNotifications = notifications.filter(n => !n.isRead);
  const unreadBatchNotifications = unreadNotifications.filter(
    n => n.type === 'NEW_BATCH' && !n.fromAudit
  );

  // CRITICAL FIX: Audit dashboard should only show batches FROM departments TO audit
  // Batches FROM audit TO departments (fromAudit = true) should appear in department dashboards
  const pendingBatches = Array.isArray(voucherBatches)
    ? voucherBatches.filter(batch => batch && !batch.received && !batch.fromAudit)
    : [];

  // Handle opening a batch
  const handleOpenBatch = (batchId: string) => {
    try {
      // Store current state before opening batch
      if (selectedDepartment) {
        setPreviousDepartment(selectedDepartment);
      }
      setActiveBatchId(batchId);
    } catch (error) {
      console.error("Error opening batch:", error);
      toast({
        title: "Error",
        description: "Failed to open batch. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle closing a batch
  const handleCloseBatch = () => {
    try {
      setActiveBatchId(null);

      // Restore previous state if it exists
      if (previousDepartment) {
        setSelectedDepartment(previousDepartment);
        // Clear the previous department to avoid unexpected redirects
        setPreviousDepartment(null);
      }
    } catch (error) {
      console.error("Error closing batch:", error);
    }
  };

  // Handle selecting a department
  const handleSelectDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(false);
    // Reset selected vouchers when changing departments
    setSelectedDispatchVouchers([]);
    setDispatchPerson("SELECT_PERSON");
  };

  // Handle showing provisional cash
  const handleShowProvisionalCash = (department: Department) => {
    setSelectedDepartment(department);
    setShowProvisionalCash(true);
  };

  // Handle going back to department hubs
  const handleBack = () => {
    if (selectedDepartment) {
      setSelectedDepartment(null);
      setShowProvisionalCash(false);
      // Reset selected vouchers when going back
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    }
  };

  // Handle dispatching vouchers
  const handleDispatchVouchers = () => {
    if (!selectedDepartment || selectedDispatchVouchers.length === 0) {
      toast({
        title: "Error",
        description: "Please select vouchers and a department.",
        variant: "destructive",
      });
      return;
    }

    if (!dispatchPerson || dispatchPerson === "SELECT_PERSON") {
      toast({
        title: "Error",
        description: "Please select a dispatch person.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Update vouchers with dispatcher information
      selectedDispatchVouchers.forEach(voucherId => {
        // Get the current voucher to check its status
        const voucher = vouchers.find(v => v.id === voucherId);
        if (!voucher) return;

        // WORKFLOW FIX: Don't change status yet - only mark as dispatched
        // Status will change when department receives the batch
        const updateData: any = {
          auditDispatchedBy: dispatchPerson,
          dispatched: true
        };

        // CRITICAL FIX: Only include fields with valid values (no undefined)
        if (voucher.pendingReturn) {
          updateData.isReturned = true;
          updateData.pendingReturn = false;
        }

        console.log(`🔄 AUDIT DISPATCH: Updating voucher ${voucherId} with:`, updateData);
        updateVoucher(voucherId, updateData);
      });

      // Send vouchers to department
      console.log(`🔍 DISPATCHER DEBUG: Selected dispatcher is "${dispatchPerson}"`);
      console.log(`🔍 DISPATCHER DEBUG: Sending to sendVouchersFromAuditToDepartment with dispatcher: "${dispatchPerson}"`);
      sendVouchersFromAuditToDepartment(selectedDepartment, selectedDispatchVouchers, dispatchPerson);

      toast({
        title: "Success",
        description: `${selectedDispatchVouchers.length} vouchers dispatched to ${selectedDepartment}.`,
      });

      // Reset selection after dispatch
      setSelectedDispatchVouchers([]);
      setDispatchPerson("SELECT_PERSON");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to dispatch vouchers.",
        variant: "destructive",
      });
    }
  };

  // Check if there are pending batches
  const hasPendingBatches = pendingBatches.length > 0;

  // Determine if the view should be locked
  const isViewLocked = hasPendingBatches && !activeBatchId;

  // Get the first valid batch ID for receiving
  const firstBatchId = hasPendingBatches && pendingBatches[0]?.id;

  // Handle receiving vouchers
  const handleReceiveVouchers = () => {
    if (firstBatchId) {
      handleOpenBatch(firstBatchId);
    }
  };

  return (
    <div className="container space-y-6">
      {/* Unread notifications */}
      {unreadBatchNotifications.length > 0 && (
        <div className="bg-amber-100 dark:bg-amber-900/20 p-4 rounded-lg flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-amber-500" />
          <p className="text-sm text-amber-800 dark:text-amber-300 uppercase">
            YOU HAVE {unreadBatchNotifications.length} UNREAD NOTIFICATION{unreadBatchNotifications.length !== 1 ? 'S' : ''} FOR NEW VOUCHER BATCH{unreadBatchNotifications.length !== 1 ? 'ES' : ''}.
          </p>
        </div>
      )}

      {/* Main content */}
      {isViewLocked ? (
        <div className="space-y-6 mb-8">
          {/* Show newly arrived vouchers */}
          <NewlyArrivedVouchers
            pendingBatches={pendingBatches}
            onOpenBatch={handleOpenBatch}
          />

          {/* Show department hubs but make them disabled */}
          <div className="opacity-60 pointer-events-none">
            <DepartmentVoucherHubs
              onSelectDepartment={handleSelectDepartment}
              onShowProvisionalCash={handleShowProvisionalCash}
            />
          </div>
        </div>
      ) : selectedDepartment ? (
        showProvisionalCash ? (
          <DepartmentProvisionalCash
            department={selectedDepartment}
            onBack={handleBack}
          />
        ) : (
          <>
            <DepartmentVoucherHub
              department={selectedDepartment}
              auditUsers={getAuditUsers().filter(user => user !== "SELECT_PERSON")}
              onBackToHubs={handleBack}
            />

            {selectedDispatchVouchers.length > 0 && (
              <DispatchControls
                selectedDispatchVouchers={selectedDispatchVouchers}
                selectedDepartment={selectedDepartment}
                dispatchPerson={dispatchPerson}
                setDispatchPerson={setDispatchPerson}
                handleDispatchVouchers={handleDispatchVouchers}
                isEditable={isDispatchEditable}
              />
            )}
          </>
        )
      ) : (
        <>
          {hasPendingBatches && (
            <div className="space-y-6 mb-8">
              <NewlyArrivedVouchers
                pendingBatches={pendingBatches}
                onOpenBatch={handleOpenBatch}
              />
            </div>
          )}

          <DepartmentVoucherHubs
            onSelectDepartment={handleSelectDepartment}
            onShowProvisionalCash={handleShowProvisionalCash}
          />
        </>
      )}

      {/* Batch receiving dialog */}
      {activeBatchId && (
        <VoucherBatchReceiving
          batchId={activeBatchId}
          open={!!activeBatchId}
          onClose={handleCloseBatch}
        />
      )}

      {/* PRODUCTION-LEVEL: Performance Monitor for Testing */}
      <div className="mt-8">
        <PerformanceMonitor />
      </div>
    </div>
  );

  function getAuditUsers() {
    // PRODUCTION FIX: Get actual audit users from database (not online status)
    const allUsers = useAppStore.getState().users || [];
    const auditUsers = allUsers.filter(user => user.department === 'AUDIT' && user.isActive);
    const userNames = auditUsers.map(user => user.name);

    // Always include current user if they're in audit
    if (currentUser && currentUser.department === 'AUDIT' && !userNames.includes(currentUser.name)) {
      userNames.unshift(currentUser.name);
    }

    return [
      "SELECT_PERSON",
      ...userNames
      // REMOVED: "GUEST" - Security requirement: No GUEST operations allowed
    ];
  }
}
