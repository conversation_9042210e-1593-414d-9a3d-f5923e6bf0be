{"name": "vms-client", "version": "1.0.0", "description": "VMS Client Launcher - Automatic Server Discovery", "main": "vms-client.js", "bin": {"vms-client": "./vms-client.js"}, "scripts": {"start": "node vms-client.js", "build": "pkg vms-client.js --targets node18-win-x64 --output VMS-Client.exe", "build-all": "pkg vms-client.js --targets node18-win-x64,node18-macos-x64,node18-linux-x64 --output VMS-Client", "install-pkg": "npm install -g pkg", "setup": "npm install && npm run install-pkg", "test": "node vms-client.js"}, "keywords": ["vms", "client", "launcher", "service-discovery", "chrome", "browser"], "author": "VMS Development Team", "license": "MIT", "dependencies": {"node-fetch": "^2.6.7"}, "devDependencies": {"pkg": "^5.8.1"}, "pkg": {"scripts": ["vms-client.js"], "assets": [], "targets": ["node18-win-x64"], "outputPath": "dist"}, "engines": {"node": ">=14.0.0"}}