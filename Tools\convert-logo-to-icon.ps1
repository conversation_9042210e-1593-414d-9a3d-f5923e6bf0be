# Convert VMS Logo to Windows Icon Format
Write-Host "Converting VMS logo to Windows icon format..." -ForegroundColor Green

# Check if logo exists
if (-not (Test-Path "vms logo.jpg")) {
    Write-Host "Error: vms logo.jpg not found!" -ForegroundColor Red
    exit 1
}

# Load required assemblies
Add-Type -AssemblyName System.Drawing

try {
    # Load the original image
    $originalImage = [System.Drawing.Image]::FromFile("vms logo.jpg")
    
    # Create a new bitmap with icon size (32x32 is standard)
    $iconSize = 32
    $iconBitmap = New-Object System.Drawing.Bitmap($iconSize, $iconSize)
    
    # Create graphics object and draw resized image
    $graphics = [System.Drawing.Graphics]::FromImage($iconBitmap)
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.DrawImage($originalImage, 0, 0, $iconSize, $iconSize)
    
    # Save as ICO file
    $iconPath = "vms-logo.ico"
    
    # Convert to icon using .NET method
    $icon = [System.Drawing.Icon]::FromHandle($iconBitmap.GetHicon())
    $fileStream = [System.IO.File]::Create($iconPath)
    $icon.Save($fileStream)
    $fileStream.Close()
    
    # Clean up
    $graphics.Dispose()
    $iconBitmap.Dispose()
    $originalImage.Dispose()
    $icon.Dispose()
    
    Write-Host "✅ Successfully created vms-logo.ico" -ForegroundColor Green
    Write-Host "📁 Icon file: $iconPath" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Error converting logo: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
