-- ================================================================
-- VMS PRODUCTION DATABASE SCHEMA FIX
-- Fixes critical database issues for production deployment
-- ================================================================

-- Fix 1: Add missing user_name column to audit_logs table
-- This column is required for audit logging functionality
ALTER TABLE audit_logs 
ADD COLUMN IF NOT EXISTS user_name VARCHAR(255) DEFAULT NULL 
COMMENT 'Username for audit trail';

-- Fix 2: Add missing last_selected_year column to users table  
-- This column is required for user year selection functionality
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_selected_year INT DEFAULT NULL 
COMMENT 'Last selected financial year by user';

-- Fix 3: Ensure audit_logs table has all required columns
-- Check and add any other missing columns that might be needed
ALTER TABLE audit_logs 
ADD COLUMN IF NOT EXISTS id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
ADD COLUMN IF NOT EXISTS timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS user_id VARCHAR(36) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS department VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS action VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS description TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS resource_type VARCHAR(100) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS resource_id VARCHAR(36) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS details JSON DEFAULT NULL,
ADD COLUMN IF NOT EXISTS ip_address VARCHAR(45) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS user_agent TEXT DEFAULT NULL,
ADD COLUMN IF NOT EXISTS severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium';

-- Fix 4: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_department ON audit_logs(department);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);

-- Fix 5: Create indexes on users table
CREATE INDEX IF NOT EXISTS idx_users_last_selected_year ON users(last_selected_year);

-- Fix 6: Verify table structures
-- Show the current structure of critical tables
DESCRIBE audit_logs;
DESCRIBE users;

-- Fix 7: Clean up any orphaned data
-- Remove any invalid audit log entries that might cause issues
DELETE FROM audit_logs WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM users);

-- Success message
SELECT 'VMS Database Schema Fixed Successfully!' as status,
       'All missing columns added and indexes created' as message,
       NOW() as timestamp;
