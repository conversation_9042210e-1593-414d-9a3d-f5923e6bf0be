using System;
using System.Windows.Forms;
using System.Drawing;

namespace VMSClientSimple
{
    public class SimpleVMSClient : Form
    {
        public SimpleVMSClient()
        {
            // Basic form setup
            this.Text = "VMS Client Test";
            this.Size = new Size(400, 200);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.LightBlue;
            
            // Add a simple label
            Label testLabel = new Label();
            testLabel.Text = "VMS Client GUI Test - If you see this, GUI works!";
            testLabel.Font = new Font("Arial", 12, FontStyle.Bold);
            testLabel.ForeColor = Color.DarkBlue;
            testLabel.TextAlign = ContentAlignment.MiddleCenter;
            testLabel.Dock = DockStyle.Fill;
            
            this.Controls.Add(testLabel);
            
            // Auto-close after 5 seconds
            Timer closeTimer = new Timer();
            closeTimer.Interval = 5000;
            closeTimer.Tick += (s, e) => {
                closeTimer.Stop();
                this.Close();
            };
            closeTimer.Start();
        }
    }

    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new SimpleVMSClient());
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error: " + ex.Message, "VMS Client Error");
            }
        }
    }
}
