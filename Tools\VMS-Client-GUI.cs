using System;
using System.Drawing;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Net;
using Microsoft.Win32;

namespace VMSClient
{
    public partial class VMSClientForm : Form
    {
        private Label titleLabel;
        private Label statusLabel;
        private PictureBox spinnerBox;
        private ProgressBar progressBar;
        private Button retryButton;
        private Timer spinnerTimer;
        private int spinnerAngle = 0;
        private HttpClient httpClient;
        private string foundServerUrl = "";

        public VMSClientForm()
        {
            InitializeComponent();
            httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(3);
        }

        private void InitializeComponent()
        {
            // Form setup
            this.Text = "VMS Client";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255); // Light blue background
            this.Icon = SystemIcons.Application;

            // Title Label
            titleLabel = new Label();
            titleLabel.Text = "VMS SYSTEM CLIENT";
            titleLabel.Font = new Font("Segoe UI", 18, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(25, 118, 210); // Blue color
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.Location = new Point(50, 30);
            titleLabel.Size = new Size(400, 40);
            this.Controls.Add(titleLabel);

            // Subtitle
            Label subtitleLabel = new Label();
            subtitleLabel.Text = "Automatic Server Connection";
            subtitleLabel.Font = new Font("Segoe UI", 10, FontStyle.Regular);
            subtitleLabel.ForeColor = Color.FromArgb(100, 100, 100);
            subtitleLabel.TextAlign = ContentAlignment.MiddleCenter;
            subtitleLabel.Location = new Point(50, 75);
            subtitleLabel.Size = new Size(400, 25);
            this.Controls.Add(subtitleLabel);

            // Spinner PictureBox
            spinnerBox = new PictureBox();
            spinnerBox.Location = new Point(225, 120);
            spinnerBox.Size = new Size(50, 50);
            spinnerBox.BackColor = Color.Transparent;
            spinnerBox.Paint += SpinnerBox_Paint;
            this.Controls.Add(spinnerBox);

            // Status Label
            statusLabel = new Label();
            statusLabel.Text = "Please wait while we connect you to the VMS SYSTEM";
            statusLabel.Font = new Font("Segoe UI", 11, FontStyle.Regular);
            statusLabel.ForeColor = Color.FromArgb(66, 66, 66);
            statusLabel.TextAlign = ContentAlignment.MiddleCenter;
            statusLabel.Location = new Point(50, 185);
            statusLabel.Size = new Size(400, 50);
            this.Controls.Add(statusLabel);

            // Progress Bar
            progressBar = new ProgressBar();
            progressBar.Location = new Point(100, 250);
            progressBar.Size = new Size(300, 20);
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.MarqueeAnimationSpeed = 50;
            progressBar.ForeColor = Color.FromArgb(25, 118, 210);
            this.Controls.Add(progressBar);

            // Retry Button (initially hidden)
            retryButton = new Button();
            retryButton.Text = "Try Again";
            retryButton.Font = new Font("Segoe UI", 10, FontStyle.Regular);
            retryButton.BackColor = Color.FromArgb(25, 118, 210);
            retryButton.ForeColor = Color.White;
            retryButton.FlatStyle = FlatStyle.Flat;
            retryButton.FlatAppearance.BorderSize = 0;
            retryButton.Location = new Point(200, 280);
            retryButton.Size = new Size(100, 35);
            retryButton.Visible = false;
            retryButton.Click += RetryButton_Click;
            this.Controls.Add(retryButton);

            // Spinner Timer
            spinnerTimer = new Timer();
            spinnerTimer.Interval = 50; // 50ms for smooth animation
            spinnerTimer.Tick += SpinnerTimer_Tick;
        }

        private void SpinnerTimer_Tick(object sender, EventArgs e)
        {
            spinnerAngle += 10;
            if (spinnerAngle >= 360) spinnerAngle = 0;
            spinnerBox.Invalidate(); // Trigger repaint
        }

        private void SpinnerBox_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

            // Draw spinning circle
            using (Pen pen = new Pen(Color.FromArgb(25, 118, 210), 4))
            {
                pen.StartCap = System.Drawing.Drawing2D.LineCap.Round;
                pen.EndCap = System.Drawing.Drawing2D.LineCap.Round;

                // Draw arc that rotates
                Rectangle rect = new Rectangle(5, 5, 40, 40);
                g.DrawArc(pen, rect, spinnerAngle, 270);
            }
        }

        protected override async void OnShown(EventArgs e)
        {
            base.OnShown(e);
            spinnerTimer.Start();
            await StartServerDiscovery();
        }

        private async Task StartServerDiscovery()
        {
            try
            {
                UpdateStatus("Searching for VMS Server...");

                // Add delay to show the beautiful GUI
                await Task.Delay(2000);

                // Test common addresses first
                string[] commonAddresses = {
                    "http://localhost:8080",
                    "http://127.0.0.1:8080"
                };

                foreach (string url in commonAddresses)
                {
                    if (await TestServer(url))
                    {
                        foundServerUrl = url;
                        await ConnectToServer();
                        return;
                    }
                }

                UpdateStatus("Scanning network...");

                // Get network range and scan
                string networkBase = GetNetworkBase();
                if (!string.IsNullOrEmpty(networkBase))
                {
                    string[] networkAddresses = {
                        "http://" + networkBase + ".1:8080",
                        "http://" + networkBase + ".100:8080",
                        "http://" + networkBase + ".101:8080",
                        "http://" + networkBase + ".102:8080",
                        "http://" + networkBase + ".200:8080",
                        "http://" + networkBase + ".250:8080"
                    };

                    foreach (string url in networkAddresses)
                    {
                        if (await TestServer(url))
                        {
                            foundServerUrl = url;
                            await ConnectToServer();
                            return;
                        }
                    }
                }

                // Server not found
                ShowError("VMS Server not found on network", 
                         "Please ensure the VMS Server is running and you are connected to the same network.");
            }
            catch (Exception ex)
            {
                ShowError("Connection Error", "An error occurred while searching for the server: " + ex.Message);
            }
        }

        private async Task<bool> TestServer(string url)
        {
            try
            {
                var response = await httpClient.GetAsync(url + "/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private string GetNetworkBase()
        {
            try
            {
                foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.OperationalStatus == OperationalStatus.Up && 
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        foreach (UnicastIPAddressInformation ip in ni.GetIPProperties().UnicastAddresses)
                        {
                            if (ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                            {
                                string ipStr = ip.Address.ToString();
                                if (!ipStr.StartsWith("127.") && !ipStr.StartsWith("169.254."))
                                {
                                    string[] parts = ipStr.Split('.');
                                    return parts[0] + "." + parts[1] + "." + parts[2];
                                }
                            }
                        }
                    }
                }
            }
            catch { }
            return "";
        }

        private async Task ConnectToServer()
        {
            UpdateStatus("VMS Server found! Opening browser...");
            
            await Task.Delay(1000); // Brief pause for user to see success message

            try
            {
                string chromePath = FindChrome();
                if (!string.IsNullOrEmpty(chromePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = chromePath,
                        Arguments = "--new-window --start-maximized \"" + foundServerUrl + "\"",
                        UseShellExecute = false
                    });
                }
                else
                {
                    // Fallback to default browser
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = foundServerUrl,
                        UseShellExecute = true
                    });
                }

                UpdateStatus("VMS System opened successfully!");
                spinnerTimer.Stop();
                progressBar.Style = ProgressBarStyle.Continuous;
                progressBar.Value = 100;

                await Task.Delay(2000);
                this.Close();
            }
            catch (Exception ex)
            {
                ShowError("Browser Error", "Could not open browser: " + ex.Message);
            }
        }

        private string FindChrome()
        {
            string[] chromePaths = {
                @"C:\Program Files\Google\Chrome\Application\chrome.exe",
                @"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                      @"Google\Chrome\Application\chrome.exe")
            };

            foreach (string path in chromePaths)
            {
                if (System.IO.File.Exists(path))
                    return path;
            }

            return "";
        }

        private void UpdateStatus(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateStatus), message);
                return;
            }
            statusLabel.Text = message;
        }

        private void ShowError(string title, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, string>(ShowError), title, message);
                return;
            }

            spinnerTimer.Stop();
            progressBar.Visible = false;
            statusLabel.Text = message;
            statusLabel.ForeColor = Color.FromArgb(211, 47, 47); // Red color
            retryButton.Visible = true;
        }

        private async void RetryButton_Click(object sender, EventArgs e)
        {
            retryButton.Visible = false;
            progressBar.Visible = true;
            statusLabel.ForeColor = Color.FromArgb(66, 66, 66);
            spinnerTimer.Start();
            await StartServerDiscovery();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (spinnerTimer != null) spinnerTimer.Stop();
            if (httpClient != null) httpClient.Dispose();
            base.OnFormClosed(e);
        }
    }

    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new VMSClientForm());
        }
    }
}
