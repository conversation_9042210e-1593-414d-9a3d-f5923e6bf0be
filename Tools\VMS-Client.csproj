<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>VMS-Client</AssemblyName>
    <RootNamespace>VMSClient</RootNamespace>
    
    <!-- Self-contained deployment - NO .NET required on client PCs -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    
    <!-- Production optimizations (Windows Forms compatible) -->
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <PublishTrimmed>false</PublishTrimmed>
    
    <!-- Application metadata -->
    <AssemblyTitle>VMS Client</AssemblyTitle>
    <AssemblyDescription>VMS System Client - Automatic Server Discovery</AssemblyDescription>
    <AssemblyCompany>VMS Development Team</AssemblyCompany>
    <AssemblyProduct>VMS Client</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

</Project>
