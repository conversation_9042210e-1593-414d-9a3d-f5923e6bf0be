{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Checking rollover enhancement columns...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Rollover column already exists: scheduled_rollover_date","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Rollover column already exists: auto_rollover_enabled","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Rollover column already exists: use_live_time","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📊 Process ID: 11440","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"New database connection established: 955","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"warn","message":"⚠️ Interval-based backup scheduling is deprecated. Use scheduleDaily() for production.","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Backup scheduler initialized - waiting for admin configuration","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🕐 Starting live time synchronization service","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🕐 Live time sync service started (interval: 60s)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"✅ Live time synchronization service started (LAN-optimized)","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🖥️  VMS System: http://10.29.74.232:8080","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🔌 WebSocket: http://10.29.74.232:8080/socket.io/","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   💾 Memory: 64MB RSS","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754242979573,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:40:22.503Z to 2025-08-03T17:42:59.607Z","service":"vms-server","timestamp":"2025-08-03 17:42:59"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-03 17:43:29"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-03 17:43:29"}
{"level":"info","message":"✅ All users have unique sessions (0 unique active users)","service":"vms-server","timestamp":"2025-08-03 17:43:29"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-03 17:43:29"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"📅 Current date: 2025-08-03T17:43:59.015Z","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:42:59.607Z to 2025-08-03T17:43:59.561Z","service":"vms-server","timestamp":"2025-08-03 17:43:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:43:59.561Z to 2025-08-03T17:44:59.575Z","service":"vms-server","timestamp":"2025-08-03 17:44:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:44:59.575Z to 2025-08-03T17:45:59.580Z","service":"vms-server","timestamp":"2025-08-03 17:45:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:45:59.580Z to 2025-08-03T17:46:59.587Z","service":"vms-server","timestamp":"2025-08-03 17:46:59"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 17:47:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:46:59.587Z to 2025-08-03T17:47:59.595Z","service":"vms-server","timestamp":"2025-08-03 17:47:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:47:59.595Z to 2025-08-03T17:48:59.606Z","service":"vms-server","timestamp":"2025-08-03 17:48:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:48:59.606Z to 2025-08-03T17:49:59.621Z","service":"vms-server","timestamp":"2025-08-03 17:49:59"}
{"level":"info","message":"\u001b[0mGET /health \u001b[32m200\u001b[0m 3.301 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:22"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 2.656 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:24"}
{"level":"info","message":"\u001b[0mGET /assets/index-B3EUqi2L-*************.css \u001b[32m200\u001b[0m 1.174 ms - 96197\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-*************.js \u001b[32m200\u001b[0m 1.381 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /assets/ui-7fVntM3B-*************.js \u001b[32m200\u001b[0m 3.305 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-*************.js \u001b[32m200\u001b[0m 1.868 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /assets/index-C1uTKUg2-*************.js \u001b[32m200\u001b[0m 1.335 ms - 1220572\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T17:50:25.692Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.474 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.897 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:50:25.813Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 6.350 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T17:50:25.912Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:50:25.945Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.156 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:25"}
{"ip":"************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T17:50:43.575Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: MR. FELIX AYISI (FINANCE) - Role: USER","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged in to FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Created new session for user MR. FELIX AYISI (session: 8e7f8195-a3c3-444d-ad84-4ddaf66326b8)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 62.035 ms - 267\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.024 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: GET /vouchers?department=FINANCE&timestamp=1754108337821","service":"vms-server","timestamp":"2025-08-03T17:50:43.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=FINANCE&timestamp=1754108337821","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T17:50:43.656Z by user MR. FELIX AYISI (FINANCE), query department: FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Found 5 vouchers for request from MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Sample vouchers: FINAUG0005 (2f6cab1d-c723-422b-be10-bfa5a9c30fb5): VOUCHER CERTIFIED, FINAUG0004 (ee05fb30-4d80-4f34-abdb-1bac4391096c): PENDING SUBMISSION, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-03 17:22:13","receivedBy":"MR. FELIX AYISI","receivedByAudit":true,"service":"vms-server","status":"VOUCHER CERTIFIED","timestamp":"2025-08-03 17:50:43","voucherId":"FINAUG0005"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=FINANCE&timestamp=1754108337821 \u001b[32m200\u001b[0m 23.060 ms - 8327\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Socket inH3TRMNo_OnQ0T3AAAB is in rooms: inH3TRMNo_OnQ0T3AAAB, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.097 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T17:50:43.831Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 18.322 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Existing connection: inH3TRMNo_OnQ0T3AAAB with last activity at 2025-08-03T17:50:43.715Z","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T17:50:43.860Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Total connected clients: 2, 2 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Socket iy9QLKiZVZfruOCOAAAE is in rooms: iy9QLKiZVZfruOCOAAAE, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 15.211 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:50:43.947Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:43.949Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.813 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 9.048 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T17:50:43.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:43.960Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.188 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 23.532 ms - 22060\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:43"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"🧹 Cleaning up stale connection: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:50:47"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:48.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.882 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:48.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.548 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:53.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.254 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:53.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.570 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:59.052Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.379 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:59"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:50:59.056Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.606 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:50:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:49:59.621Z to 2025-08-03T17:50:59.625Z","service":"vms-server","timestamp":"2025-08-03 17:50:59"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:03.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.938 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:03.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.823 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:08.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.322 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:08.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.359 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:08"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T17:51:12.775Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-02T04:19:26.964Z","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 1.196 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session 8e7f8195-a3c3-444d-ad84-4ddaf66326b8 found and deactivated","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 16.677 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T17:51:12.796Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"warn","message":"Invalid session ID: 8e7f8195-a3c3-444d-ad84-4ddaf66326b8","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[33m401\u001b[0m 4.133 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T17:51:12.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"warn","message":"Invalid session ID: 8e7f8195-a3c3-444d-ad84-4ddaf66326b8","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 5.354 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:51:12.808Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.301 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - last connection closed","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T17:51:12.854Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.449 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:51:12.892Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.770 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T17:51:12.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-03 17:51:12"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:51:13.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:51:13"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.224 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:13"}
{"level":"info","message":"\u001b[0mGET /health \u001b[32m200\u001b[0m 1.125 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:28"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 2.958 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:29"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-*************.js \u001b[32m200\u001b[0m 5.931 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /assets/index-B3EUqi2L-*************.css \u001b[32m200\u001b[0m 4.431 ms - 96197\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /assets/ui-7fVntM3B-*************.js \u001b[32m200\u001b[0m 5.348 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-*************.js \u001b[32m200\u001b[0m 4.657 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /assets/index-C1uTKUg2-*************.js \u001b[32m200\u001b[0m 5.538 ms - 1220572\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T17:51:30.053Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.129 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.590 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:51:30.097Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 2.876 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T17:51:30.148Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:51:30.160Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.287 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T17:51:42.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: MR. FELIX AYISI (FINANCE) - Role: USER","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged in to FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Created new session for user MR. FELIX AYISI (session: 843e8f27-58f6-4fef-a250-6a24a7db1739)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 34.293 ms - 267\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.634 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=FINANCE&timestamp=1754243502838","service":"vms-server","timestamp":"2025-08-03T17:51:42.843Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=FINANCE&timestamp=1754243502838","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T17:51:42.846Z by user MR. FELIX AYISI (FINANCE), query department: FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Found 5 vouchers for request from MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Sample vouchers: FINAUG0005 (2f6cab1d-c723-422b-be10-bfa5a9c30fb5): VOUCHER CERTIFIED, FINAUG0004 (ee05fb30-4d80-4f34-abdb-1bac4391096c): PENDING SUBMISSION, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-03 17:22:13","receivedBy":"MR. FELIX AYISI","receivedByAudit":true,"service":"vms-server","status":"VOUCHER CERTIFIED","timestamp":"2025-08-03 17:51:42","voucherId":"FINAUG0005"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=FINANCE&timestamp=1754243502838 \u001b[32m200\u001b[0m 12.908 ms - 8327\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Socket XsEilOogoAQ0KLYMAAAH is in rooms: XsEilOogoAQ0KLYMAAAH, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.174 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T17:51:42.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Existing connection: XsEilOogoAQ0KLYMAAAH with last activity at 2025-08-03T17:51:42.872Z","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 15.597 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:42"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T17:51:43.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"Total connected clients: 2, 2 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"Socket v94Pzne5PYTLzB1dAAAK is in rooms: v94Pzne5PYTLzB1dAAAK, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 11.281 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:51:43.038Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:43.040Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.238 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T17:51:43.044Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:43.046Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.105 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 14.026 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 26.448 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:43"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"🧹 Cleaning up stale connection: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:51:46"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:48.046Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.452 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:48"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:48.048Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.935 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:48"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:53.050Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.433 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:53"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:53.052Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.356 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:53"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:58.046Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.233 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:51:58.049Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.865 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:51:58"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:50:59.625Z to 2025-08-03T17:51:59.639Z","service":"vms-server","timestamp":"2025-08-03 17:51:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:52:03.047Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.997 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:52:03"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:52:03.050Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.422 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:52:03"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - last connection closed","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:52:06"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:52:07"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:52:07"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 17:52:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:52:13.048Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:52:13"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:52:13"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[33m401\u001b[0m 4.431 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:52:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:52:43.045Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:52:43"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:52:43"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[33m401\u001b[0m 4.439 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:52:43"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 17:52:59"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-03 17:52:59"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-03 17:52:59"}
{"level":"info","message":"✅ All users have unique sessions (0 unique active users)","service":"vms-server","timestamp":"2025-08-03 17:52:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:51:59.639Z to 2025-08-03T17:52:59.644Z","service":"vms-server","timestamp":"2025-08-03 17:52:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:11.509Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.099 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:11"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:11.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.078 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:11"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:12.457Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.061 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:12"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /vouchers","service":"vms-server","timestamp":"2025-08-03T17:53:12.464Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /","service":"vms-server","timestamp":"2025-08-03 17:53:12"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:53:12"}
{"level":"info","message":"\u001b[0mPOST /api/vouchers \u001b[33m401\u001b[0m 4.267 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:12"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:53:13.050Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:53:13"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:53:13"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[33m401\u001b[0m 4.834 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:15.465Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.515 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:15"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:15.468Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.882 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:15"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:20.468Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.890 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:20.471Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.955 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:25.468Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.865 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:25.471Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.083 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:30.850Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.977 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:30.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.445 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:35.852Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.843 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:35"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:35.856Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.036 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:35"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:40.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.201 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:40"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:40.865Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.463 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:40"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:42.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.054 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:42"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.918 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:42"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:53:43.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:53:43"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:53:43"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[33m401\u001b[0m 4.025 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:45.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.411 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:45.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.171 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:50.857Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.873 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:50.861Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.627 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:50"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:55.865Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.836 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:55"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:53:55.867Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.645 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:53:55"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:52:59.644Z to 2025-08-03T17:53:59.652Z","service":"vms-server","timestamp":"2025-08-03 17:53:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:00.907Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.242 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:00"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:00.909Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.820 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:00"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:05.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.422 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:05.860Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.178 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:10.865Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.151 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:10.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.989 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:12.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.258 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:12"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:54:13.848Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:54:13"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:54:13"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[33m401\u001b[0m 3.860 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:15.868Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.811 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:15"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:15.871Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.586 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:15"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:20.860Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.866 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:20.863Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.337 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:20"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:25.466Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.651 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:25.470Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.342 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:30.475Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.948 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:54:30.480Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.012 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T17:54:30.710Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T17:54:30.706Z","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session 843e8f27-58f6-4fef-a250-6a24a7db1739 not found or already inactive","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 7.324 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 1.168 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T17:54:30.736Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[33m401\u001b[0m 11.492 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754243670728 \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T17:54:30.764Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754243670728","service":"vms-server","timestamp":"2025-08-03T17:54:30.766Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"warn","message":"Invalid session ID: 843e8f27-58f6-4fef-a250-6a24a7db1739","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T17:54:30.800Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.377 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:54:30.858Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 25.276 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T17:54:30.919Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:54:30.949Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 37.521 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:54:30"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:53:59.652Z to 2025-08-03T17:54:59.658Z","service":"vms-server","timestamp":"2025-08-03 17:54:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"24MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 17:55:43","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"24MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 17:55:43","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:54:59.658Z to 2025-08-03T17:55:59.671Z","service":"vms-server","timestamp":"2025-08-03 17:55:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"24MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 17:56:42","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"24MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 17:56:43","uptime":"0h"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 6.535 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.704 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T17:56:44.696Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.335 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:56:44.733Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.640 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T17:56:44.774Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T17:56:44.826Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 2.784 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:44"}
{"ip":"************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T17:56:57.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: MR. FELIX AYISI (FINANCE) - Role: USER","service":"vms-server","timestamp":"2025-08-03 17:56:57"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged in to FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Created new session for user MR. FELIX AYISI (session: d31239e0-fa57-4feb-acc8-3cdcc6ebaebd)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 52.440 ms - 267\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.709 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: GET /vouchers?department=FINANCE&timestamp=1754108712237","service":"vms-server","timestamp":"2025-08-03T17:56:58.045Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=FINANCE&timestamp=1754108712237","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T17:56:58.048Z by user MR. FELIX AYISI (FINANCE), query department: FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Found 5 vouchers for request from MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Sample vouchers: FINAUG0005 (2f6cab1d-c723-422b-be10-bfa5a9c30fb5): VOUCHER CERTIFIED, FINAUG0004 (ee05fb30-4d80-4f34-abdb-1bac4391096c): PENDING SUBMISSION, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-03 17:22:13","receivedBy":"MR. FELIX AYISI","receivedByAudit":true,"service":"vms-server","status":"VOUCHER CERTIFIED","timestamp":"2025-08-03 17:56:58","voucherId":"FINAUG0005"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=FINANCE&timestamp=1754108712237 \u001b[32m200\u001b[0m 9.497 ms - 8327\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Socket mzOC_uwvZZFOugNNAAAO is in rooms: mzOC_uwvZZFOugNNAAAO, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.289 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T17:56:58.187Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 7.427 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Existing connection: mzOC_uwvZZFOugNNAAAO with last activity at 2025-08-03T17:56:58.153Z","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T17:56:58.209Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Total connected clients: 2, 2 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Socket q9jBf2N2VwijDc-9AAAR is in rooms: q9jBf2N2VwijDc-9AAAR, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 5.926 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:56:58.262Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:56:58.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.333 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T17:56:58.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:56:58.277Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.614 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 26.564 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 28.084 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:56:58"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:55:59.671Z to 2025-08-03T17:56:59.685Z","service":"vms-server","timestamp":"2025-08-03 17:56:59"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"🧹 Cleaning up stale connection: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:02"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:03.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.330 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:03.279Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.220 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:08.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.344 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:08.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.348 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:13.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.908 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:13.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.523 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:18.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.727 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:18.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.282 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:23.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.041 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:23.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 0.978 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:28.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.023 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:28"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:57:28.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:28.281Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.643 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:28"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:57:28"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 23.392 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:33.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.282 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:33.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.416 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:38.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.363 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:38.380Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.738 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:43.285Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.671 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:43.288Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.401 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:48.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.089 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:48.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.590 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:53.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.696 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:53.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.414 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:58.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.088 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:58"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:57:58.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:57:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:57:58.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.461 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:58"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:57:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 14.651 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:57:58"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 17:57:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:56:59.685Z to 2025-08-03T17:57:59.685Z","service":"vms-server","timestamp":"2025-08-03 17:57:59"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:03.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.233 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:03.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.242 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:08.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.591 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:08.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.895 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:13.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.456 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:13.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.323 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:18.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.970 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:18.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.862 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:23.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.167 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:23.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.513 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:28.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.532 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:28"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:58:28.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:58:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:28.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.677 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:28"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:58:28"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 22.422 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:33.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.607 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:33.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.328 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:38.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.212 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:38.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.096 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:43.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.046 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:43.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.397 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:48.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.721 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:48.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.663 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:53.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.930 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:53.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.878 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:53"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.757 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:58.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.365 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:58:58.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.874 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:58:58.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 15.891 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:58:58"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:57:59.685Z to 2025-08-03T17:58:59.694Z","service":"vms-server","timestamp":"2025-08-03 17:58:59"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:03.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.252 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:03.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.519 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:08.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.649 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:08.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.474 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:13.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.158 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:13.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.194 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:18.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.458 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:18.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.352 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:23.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.517 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:23.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.390 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:28.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.278 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:28"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:59:28.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:59:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:28.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.399 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:28"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:59:28"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 15.449 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:33.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.085 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:33.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.933 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:38.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.193 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:38.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.276 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:43.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 185.798 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:43.679Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 413.699 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:48.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.422 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:48.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.589 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:53.262Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.705 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:53.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.481 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:58.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.070 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:58"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T17:59:58.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 17:59:58"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T17:59:58.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.011 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:58"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 17:59:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 17.608 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 17:59:58"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:58:59.694Z to 2025-08-03T17:59:59.697Z","service":"vms-server","timestamp":"2025-08-03 17:59:59"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:03.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.024 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:03.272Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.424 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:03"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:08.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.439 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:08.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.196 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:13.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.932 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:13.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.254 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:18.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.344 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:18.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.452 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:23.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.255 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:23.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.252 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:23"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:28.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.391 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:28"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T18:00:28.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 18:00:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:28.269Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.439 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:28"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:00:28"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 12.784 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:28"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:33.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.589 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:33.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.535 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:33"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:38.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.585 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:38.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.450 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:38"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:43.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.386 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:43"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:43.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.377 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:43"}
{"connections":"1/1 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:00:43","uptime":"0h"}
{"connections":"1/1 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:00:43","uptime":"0h"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:48.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.527 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:48.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.576 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:48"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:53.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.723 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:00:53.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.593 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:53"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:00:55.046Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-02T04:29:09.259Z","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 2.125 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session d31239e0-fa57-4feb-acc8-3cdcc6ebaebd found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 27.888 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T18:00:55.076Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:00:55.080Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"warn","message":"Invalid session ID: d31239e0-fa57-4feb-acc8-3cdcc6ebaebd","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[33m401\u001b[0m 9.680 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:00:55.087Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 6.330 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"warn","message":"Invalid session ID: d31239e0-fa57-4feb-acc8-3cdcc6ebaebd","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:00:55.117Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 2.055 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:00:55.154Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.672 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:00:55.195Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:00:55.217Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.243 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:00:55"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T17:59:59.697Z to 2025-08-03T18:00:59.701Z","service":"vms-server","timestamp":"2025-08-03 18:00:59"}
{"ip":"************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T18:01:08.100Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: JERRY JOHN (PENSIONS) - Role: USER","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Audit logged: JERRY JOHN logged in to PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Created new session for user JERRY JOHN (session: aa4eefa8-4843-4c64-bf06-442a176f24b9)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 49.981 ms - 263\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.619 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /vouchers?department=PENSIONS&timestamp=1754108962381","service":"vms-server","timestamp":"2025-08-03T18:01:08.301Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=PENSIONS&timestamp=1754108962381","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T18:01:08.309Z by user JERRY JOHN (PENSIONS), query department: PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Fetching vouchers for specified department: PENSIONS (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Found 4 vouchers for request from JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Sample vouchers: PENAUG0009 (f513d04d-aae1-4702-9057-5a9a87cbcdd8): PENDING SUBMISSION, PENAUG0008 (11b55772-5404-4a71-a1e9-b8250046da49): PENDING SUBMISSION, PENAUG0007 (dfae4241-8f1b-4042-88c1-ef04e92492e7): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":false,"service":"vms-server","status":"PENDING SUBMISSION","timestamp":"2025-08-03 18:01:08","voucherId":"PENAUG0009"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=PENSIONS&timestamp=1754108962381 \u001b[32m200\u001b[0m 17.871 ms - 6113\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔌 WebSocket connection established: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🚀 Setting up authenticated connection for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined room: department:PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined personal room: user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Updated existing session with socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Total connected clients: 1, 1 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Socket RNMHOhiDmNwdtSp0AAAU is in rooms: RNMHOhiDmNwdtSp0AAAU, department:PENSIONS, user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.560 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:08.504Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[32m200\u001b[0m 4.419 ms - 194\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🔌 WebSocket connection established: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"🚀 Setting up authenticated connection for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Existing connection: RNMHOhiDmNwdtSp0AAAU with last activity at 2025-08-03T18:01:08.383Z","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined room: department:PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined personal room: user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:01:08.530Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Updated existing session with socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Total connected clients: 2, 2 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Socket m4wNlq1IXGMQZ9JqAAAX is in rooms: m4wNlq1IXGMQZ9JqAAAX, department:PENSIONS, user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 5.433 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03T18:01:08.583Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:08.585Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.099 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Found 1 online users in PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENSIONS \u001b[32m200\u001b[0m 7.206 ms - 174\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:08.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.293 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:01:08.707Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:08.708Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.314 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:08.710Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.543 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03T18:01:08.713Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 10.587 ms - 2\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"Found 1 online users in PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENSIONS \u001b[36m304\u001b[0m 8.135 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:01:08.724Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 5.001 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:08"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"🧹 Cleaning up stale connection: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"User disconnected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"Cleared socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for JERRY JOHN (PENSIONS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:12"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:13.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.277 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:13.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.734 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:13"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:18.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.710 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:18"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:18.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.447 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:18"}
{"level":"info","message":"User disconnected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:22.469Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 97a5561d-13c5-4692-be66-5dddcc6819fc (browser_close) at 2025-08-02T04:29:36.684Z","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"Cleared socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"Broadcasting 0 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for JERRY JOHN (PENSIONS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 1.170 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session aa4eefa8-4843-4c64-bf06-442a176f24b9 found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 97a5561d-13c5-4692-be66-5dddcc6819fc cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 16.804 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T18:01:22.492Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"warn","message":"Invalid session ID: aa4eefa8-4843-4c64-bf06-442a176f24b9","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[33m401\u001b[0m 3.473 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:22.496Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"warn","message":"Invalid session ID: aa4eefa8-4843-4c64-bf06-442a176f24b9","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 5.360 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:01:22.505Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:01:22.506Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.732 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.771 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:01:22.547Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.763 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:01:22.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:01:22.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 2.817 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:01:22.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.940 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:22"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T18:01:32.078Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: MR. FELIX AYISI (FINANCE) - Role: USER","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged in to FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Created new session for user MR. FELIX AYISI (session: b292225e-cbf3-4c51-8cce-8c29e52d2dde)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 57.891 ms - 267\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.581 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=FINANCE&timestamp=1754244092139","service":"vms-server","timestamp":"2025-08-03T18:01:32.145Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=FINANCE&timestamp=1754244092139","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T18:01:32.149Z by user MR. FELIX AYISI (FINANCE), query department: FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Found 5 vouchers for request from MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Sample vouchers: FINAUG0005 (2f6cab1d-c723-422b-be10-bfa5a9c30fb5): VOUCHER CERTIFIED, FINAUG0004 (ee05fb30-4d80-4f34-abdb-1bac4391096c): PENDING SUBMISSION, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-03 17:22:13","receivedBy":"MR. FELIX AYISI","receivedByAudit":true,"service":"vms-server","status":"VOUCHER CERTIFIED","timestamp":"2025-08-03 18:01:32","voucherId":"FINAUG0005"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=FINANCE&timestamp=1754244092139 \u001b[32m200\u001b[0m 11.615 ms - 8327\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Socket DQRfuOLKEJMy8-zJAAAa is in rooms: DQRfuOLKEJMy8-zJAAAa, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 10.448 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:32.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Existing connection: DQRfuOLKEJMy8-zJAAAa with last activity at 2025-08-03T18:01:32.173Z","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[32m200\u001b[0m 26.253 ms - 194\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Total connected clients: 2, 2 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Socket 1qwidwFfqX-C9zktAAAd is in rooms: 1qwidwFfqX-C9zktAAAd, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:01:32.308Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 9.602 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T18:01:32.340Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:32.342Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.499 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:01:32.348Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:32.353Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.404 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 18.221 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 24.233 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:32"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"🧹 Cleaning up stale connection: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:36"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:37.349Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.946 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:37"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:01:37.351Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.370 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:37"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:38.716Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:38.711Z","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /dashboard \u001b[32m200\u001b[0m 3.112 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 20.641 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:38.738Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:38.729Z","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 3.932 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-*************.js \u001b[32m200\u001b[0m 3.921 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /assets/index-B3EUqi2L-*************.css \u001b[32m200\u001b[0m 3.469 ms - 96197\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /assets/ui-7fVntM3B-*************.js \u001b[32m200\u001b[0m 3.403 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-*************.js \u001b[32m200\u001b[0m 12.928 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /assets/index-C1uTKUg2-*************.js \u001b[32m200\u001b[0m 5.315 ms - 1220572\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:01:38.808Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.158 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.683 ms - 292\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:38.842Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 3.003 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:01:38.851Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[33m401\u001b[0m 3.926 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/available","service":"vms-server","timestamp":"2025-08-03T18:01:38.862Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /available","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /api/years/available \u001b[33m401\u001b[0m 5.819 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:38"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 4.583 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:42"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"26MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:01:42","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"26MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:01:43","uptime":"0h"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:43.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:43.260Z","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 5.529 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /dashboard \u001b[32m200\u001b[0m 2.571 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:43.288Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:43.279Z","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 4.725 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /assets/utils-khaVmnFk-*************.js \u001b[32m200\u001b[0m 2.076 ms - 20714\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /assets/ui-7fVntM3B-*************.js \u001b[32m200\u001b[0m 2.442 ms - 80749\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /assets/vendor-C6oTH5a1-*************.js \u001b[32m200\u001b[0m 2.407 ms - 140297\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /assets/index-B3EUqi2L-*************.css \u001b[32m200\u001b[0m 2.196 ms - 96197\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /assets/index-C1uTKUg2-*************.js \u001b[32m200\u001b[0m 2.462 ms - 1220572\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:01:43.351Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.323 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.568 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:43.361Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 3.889 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:01:43.370Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[33m401\u001b[0m 3.318 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /favicon.ico \u001b[32m200\u001b[0m 15.172 ms - 1150\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/available","service":"vms-server","timestamp":"2025-08-03T18:01:43.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /available","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"info","message":"\u001b[0mGET /api/years/available \u001b[33m401\u001b[0m 4.164 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /years/select","service":"vms-server","timestamp":"2025-08-03T18:01:45.373Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /select","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"level":"info","message":"\u001b[0mPOST /api/years/select \u001b[33m401\u001b[0m 3.096 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/available","service":"vms-server","timestamp":"2025-08-03T18:01:45.390Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /available","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"level":"info","message":"\u001b[0mGET /api/years/available \u001b[33m401\u001b[0m 10.756 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:45"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:49.527Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:49.520Z","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 8.896 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mGET /dashboard \u001b[36m304\u001b[0m 11.824 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:49.549Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:49.545Z","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 10.190 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:01:49.577Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.161 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.132 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:01:49.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 29.521 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:01:49.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[33m401\u001b[0m 3.231 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/available","service":"vms-server","timestamp":"2025-08-03T18:01:49.698Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /available","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"warn","message":"Invalid session ID: b292225e-cbf3-4c51-8cce-8c29e52d2dde","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"level":"info","message":"\u001b[0mGET /api/years/available \u001b[33m401\u001b[0m 5.064 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:52.044Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:52.040Z","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:01:52.049Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:01:52.044Z","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 10.112 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"warn","message":"⚠️ IMMEDIATE LOGOUT: Session b292225e-cbf3-4c51-8cce-8c29e52d2dde not found or already inactive","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 8.722 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:01:52"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"27MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:01:58","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"27MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:01:58","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:00:59.701Z to 2025-08-03T18:01:59.704Z","service":"vms-server","timestamp":"2025-08-03 18:01:59"}
{"level":"info","message":"\u001b[0mGET /health \u001b[32m200\u001b[0m 0.594 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:03"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 1.395 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:02:05.495Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.360 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.792 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:05.539Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.010 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:02:05.591Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:05.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.618 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:05"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T18:02:13.090Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: MR. FELIX AYISI (FINANCE) - Role: USER","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged in to FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Created new session for user MR. FELIX AYISI (session: 46c8e461-0c7d-4e9e-af35-4a57d8fc637d)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 48.306 ms - 267\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.567 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=FINANCE&timestamp=1754244133141","service":"vms-server","timestamp":"2025-08-03T18:02:13.147Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=FINANCE&timestamp=1754244133141","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T18:02:13.151Z by user MR. FELIX AYISI (FINANCE), query department: FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Fetching vouchers for specified department: FINANCE (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Found 5 vouchers for request from MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Sample vouchers: FINAUG0005 (2f6cab1d-c723-422b-be10-bfa5a9c30fb5): VOUCHER CERTIFIED, FINAUG0004 (ee05fb30-4d80-4f34-abdb-1bac4391096c): PENDING SUBMISSION, FINAUG0003 (e7f8f40a-91e5-41cf-92f8-2ac574cb6b34): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-03 17:22:13","receivedBy":"MR. FELIX AYISI","receivedByAudit":true,"service":"vms-server","status":"VOUCHER CERTIFIED","timestamp":"2025-08-03 18:02:13","voucherId":"FINAUG0005"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=FINANCE&timestamp=1754244133141 \u001b[32m200\u001b[0m 9.564 ms - 8327\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Socket XCTK22lCNm1npQqTAAAp is in rooms: XCTK22lCNm1npQqTAAAp, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:02:13.283Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 3.526 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[32m200\u001b[0m 10.808 ms - 194\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: MR. FELIX AYISI (FINANCE)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🔌 WebSocket connection established: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"🚀 Setting up authenticated connection for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Existing connection: XCTK22lCNm1npQqTAAAp with last activity at 2025-08-03T18:02:13.170Z","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined room: department:FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) joined personal room: user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:02:13.304Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Updated existing session with socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84)","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Total connected clients: 2, 2 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Socket UdTSjXEw6Kpvs9YJAAAs is in rooms: UdTSjXEw6Kpvs9YJAAAs, department:FINANCE, user:30e94a63-99b2-41ed-a92e-8c4ff7c5dd84","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[32m200\u001b[0m 7.974 ms - 76\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03T18:02:13.337Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:13.339Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.466 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:02:13.342Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:13.345Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.754 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"Found 1 online users in FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=FINANCE \u001b[32m200\u001b[0m 12.332 ms - 178\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 16.241 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:13"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:02:16.286Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 (browser_close) at 2025-08-03T18:02:16.273Z","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Broadcasting 1 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"User disconnected: MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) from department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T18:02:16.295Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Cleared socket info for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Broadcasting 0 connected users for department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for MR. FELIX AYISI (FINANCE) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754244136283","service":"vms-server","timestamp":"2025-08-03T18:02:16.300Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:02:16.313Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 29.065 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:02:16.321Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session 46c8e461-0c7d-4e9e-af35-4a57d8fc637d found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 30e94a63-99b2-41ed-a92e-8c4ff7c5dd84 cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 46.607 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department FINANCE","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 10.715 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754244136283 \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:02:16.350Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.296 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"warn","message":"Invalid session ID: 46c8e461-0c7d-4e9e-af35-4a57d8fc637d","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:16.364Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 7.935 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:02:16.381Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"Audit logged: MR. FELIX AYISI logged out from FINANCE department","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"User MR. FELIX AYISI (30e94a63-99b2-41ed-a92e-8c4ff7c5dd84) logged out successfully. Session ID: 46c8e461-0c7d-4e9e-af35-4a57d8fc637d","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:16.389Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.339 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:16"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T18:02:24.363Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: JERRY JOHN (PENSIONS) - Role: USER","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Audit logged: JERRY JOHN logged in to PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Created new session for user JERRY JOHN (session: 7c190e3d-8120-4625-b959-621a3ac246c9)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 46.050 ms - 263\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.580 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=PENSIONS&timestamp=1754244144412","service":"vms-server","timestamp":"2025-08-03T18:02:24.418Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=PENSIONS&timestamp=1754244144412","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T18:02:24.421Z by user JERRY JOHN (PENSIONS), query department: PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Fetching vouchers for specified department: PENSIONS (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Found 4 vouchers for request from JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Sample vouchers: PENAUG0009 (f513d04d-aae1-4702-9057-5a9a87cbcdd8): PENDING SUBMISSION, PENAUG0008 (11b55772-5404-4a71-a1e9-b8250046da49): PENDING SUBMISSION, PENAUG0007 (dfae4241-8f1b-4042-88c1-ef04e92492e7): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":false,"service":"vms-server","status":"PENDING SUBMISSION","timestamp":"2025-08-03 18:02:24","voucherId":"PENAUG0009"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=PENSIONS&timestamp=1754244144412 \u001b[32m200\u001b[0m 7.654 ms - 6113\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔌 WebSocket connection established: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🚀 Setting up authenticated connection for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined room: department:PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined personal room: user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Updated existing session with socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Total connected clients: 1, 1 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Socket PDBgykdxlgX43zvzAAAv is in rooms: PDBgykdxlgX43zvzAAAv, department:PENSIONS, user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.295 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:02:24.540Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🔌 WebSocket connection established: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"🚀 Setting up authenticated connection for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Existing connection: PDBgykdxlgX43zvzAAAv with last activity at 2025-08-03T18:02:24.437Z","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined room: department:PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) joined personal room: user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[32m200\u001b[0m 15.047 ms - 194\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:02:24.557Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Updated existing session with socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Total connected clients: 2, 2 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Socket dLn4iSUThsuYR_PjAAAy is in rooms: dLn4iSUThsuYR_PjAAAy, department:PENSIONS, user:97a5561d-13c5-4692-be66-5dddcc6819fc","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 9.010 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03T18:02:24.582Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:24.584Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.793 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:24.588Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.000 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:02:24.589Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Found 1 online users in PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENSIONS \u001b[32m200\u001b[0m 13.270 ms - 174\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 14.030 ms - 2\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:24.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.849 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03T18:02:24.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:24.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.033 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:02:24.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 5.614 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"Found 1 online users in PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENSIONS \u001b[36m304\u001b[0m 18.020 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:24"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"🧹 Cleaning up stale connection: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc)","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"User disconnected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: JERRY JOHN (PENSIONS)","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"Cleared socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"Broadcasting 1 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for JERRY JOHN (PENSIONS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:28"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:29.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 26.864 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:29"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:29.669Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 38.867 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:29"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:34.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.498 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:34.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 4.590 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:34"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:39.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.287 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:39"}
{"ip":"127.0.0.1","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:02:39.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.764 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:39"}
{"level":"info","message":"User disconnected: JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) from department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:02:41.801Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 97a5561d-13c5-4692-be66-5dddcc6819fc (browser_close) at 2025-08-03T18:02:41.798Z","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"Cleared socket info for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"Broadcasting 0 connected users for department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for JERRY JOHN (PENSIONS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 4.153 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T18:02:41.816Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:02:41.819Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:41.823Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session 7c190e3d-8120-4625-b959-621a3ac246c9 found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 97a5561d-13c5-4692-be66-5dddcc6819fc cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 25.672 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[0m-\u001b[0m - ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 7.352 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department PENSIONS","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:02:41.849Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 1.391 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:41.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Audit logged: JERRY JOHN logged out from PENSIONS department","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"User JERRY JOHN (97a5561d-13c5-4692-be66-5dddcc6819fc) logged out successfully. Session ID: 7c190e3d-8120-4625-b959-621a3ac246c9","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 4.382 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:02:41.882Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:02:41.893Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 3.529 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:02:41"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 18:02:59"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-03 18:02:59"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-03 18:02:59"}
{"level":"info","message":"✅ All users have unique sessions (0 unique active users)","service":"vms-server","timestamp":"2025-08-03 18:02:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:01:59.704Z to 2025-08-03T18:02:59.712Z","service":"vms-server","timestamp":"2025-08-03 18:02:59"}
{"ip":"************","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-03T18:03:49.128Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: GYAMPOH (PENTSOS) - Role: USER","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Audit logged: GYAMPOH logged in to PENTSOS department","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Created new session for user GYAMPOH (session: b462f731-5a5d-4b8f-a346-49d6ef5365d0)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 45.844 ms - 259\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.604 ms - 291\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /vouchers?department=PENTSOS&timestamp=1754109123410","service":"vms-server","timestamp":"2025-08-03T18:03:49.202Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=PENTSOS&timestamp=1754109123410","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"GET /vouchers request at 2025-08-03T18:03:49.205Z by user GYAMPOH (PENTSOS), query department: PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Fetching vouchers for specified department: PENTSOS (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Found 3 vouchers for request from GYAMPOH (PENTSOS)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Sample vouchers: PSOAUG0003 (b235c938-994e-4e70-88fc-ee685482d393): AUDIT: PROCESSING, PSOAUG0002 (ebc5d49e-2075-4d43-89a4-42d18428128b): PENDING SUBMISSION, PSOAUG0001 (7ca31f51-b556-4235-a06c-5918c142fbf0): PENDING SUBMISSION","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-02 07:28:02","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-03 18:03:49","voucherId":"PSOAUG0003"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=PENTSOS&timestamp=1754109123410 \u001b[32m200\u001b[0m 11.471 ms - 4922\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: GYAMPOH (PENTSOS)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔌 WebSocket connection established: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from PENTSOS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🚀 Setting up authenticated connection for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) joined room: department:PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) joined personal room: user:24f05c6c-a7d4-49c6-a1b1-3649e5f473cb","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Broadcasting 1 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Updated existing session with socket info for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Total connected clients: 1, 1 in department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Socket AKD3eI3SoOvJ4X_tAAA1 is in rooms: AKD3eI3SoOvJ4X_tAAA1, department:PENTSOS, user:24f05c6c-a7d4-49c6-a1b1-3649e5f473cb","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 1.316 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:03:49.378Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 5.461 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"WebSocket connection from ************ - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: GYAMPOH (PENTSOS)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🔌 WebSocket connection established: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from PENTSOS [Auth: YES]","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"🚀 Setting up authenticated connection for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) already connected from 1 other sessions","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Existing connection: AKD3eI3SoOvJ4X_tAAA1 with last activity at 2025-08-03T18:03:49.249Z","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) joined room: department:PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) joined personal room: user:24f05c6c-a7d4-49c6-a1b1-3649e5f473cb","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Broadcasting 1 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-03T18:03:49.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Updated existing session with socket info for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb)","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Total connected clients: 2, 2 in department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Socket 1IYm2u388Ne4Y72NAAA4 is in rooms: 1IYm2u388Ne4Y72NAAA4, department:PENTSOS, user:24f05c6c-a7d4-49c6-a1b1-3649e5f473cb","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 11.938 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=PENTSOS","service":"vms-server","timestamp":"2025-08-03T18:03:49.461Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:49.462Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.850 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Found 1 online users in PENTSOS department","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENTSOS \u001b[32m200\u001b[0m 9.297 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:03:49.471Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:49.475Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.783 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 15.096 ms - 12541\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:49.519Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.014 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:49.521Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.020 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /users/online?department=PENTSOS","service":"vms-server","timestamp":"2025-08-03T18:03:49.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"Found 1 online users in PENTSOS department","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=PENTSOS \u001b[36m304\u001b[0m 9.630 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"ip":"************","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-03T18:03:49.530Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 9.512 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:49"}
{"level":"warn","message":"🔍 INSTANT: Stale connection detected: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) - 4s since last heartbeat","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"🧹 Cleaning up stale connection: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb)","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"User disconnected: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) still has 1 active connections","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"📢 SHARP: Broadcasting user_left immediately: GYAMPOH (PENTSOS)","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"Broadcasting 1 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"🧹 Cleaned up 1 stale connections","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"Cleared socket info for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"Broadcasting 1 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for GYAMPOH (PENTSOS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 1 in department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:53"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:54.515Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.013 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:54"}
{"ip":"************","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-03T18:03:54.522Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.963 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:54"}
{"level":"info","message":"User disconnected: GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) from department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"User GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) has no remaining connections","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: POST /auth/immediate-logout","service":"vms-server","timestamp":"2025-08-03T18:03:56.334Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚪 IMMEDIATE LOGOUT: User 24f05c6c-a7d4-49c6-a1b1-3649e5f473cb (browser_close) at 2025-08-02T04:32:10.554Z","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"Cleared socket info for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) - session remains active","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"Broadcasting 0 connected users for department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"📢 IMMEDIATE: Broadcasting user_left for GYAMPOH (PENTSOS) - disconnect event","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /?logout=true \u001b[32m200\u001b[0m 6.956 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: Session b462f731-5a5d-4b8f-a346-49d6ef5365d0 found and deactivated","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"✅ IMMEDIATE LOGOUT: User 24f05c6c-a7d4-49c6-a1b1-3649e5f473cb cleanup completed - 0 locks released","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mPOST /api/auth/immediate-logout \u001b[32m200\u001b[0m 25.997 ms - 16\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: POST /auth/logout","service":"vms-server","timestamp":"2025-08-03T18:03:56.364Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /logout","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-03T18:03:56.369Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"✅ IMMEDIATE: Deactivated session for GYAMPOH (24f05c6c-a7d4-49c6-a1b1-3649e5f473cb) - last connection closed","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department PENTSOS","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"warn","message":"Invalid session ID: b462f731-5a5d-4b8f-a346-49d6ef5365d0","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mPOST /api/auth/logout \u001b[33m401\u001b[0m 11.843 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:03:56.378Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"warn","message":"Invalid session ID: b462f731-5a5d-4b8f-a346-49d6ef5365d0","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[33m401\u001b[0m 12.633 ms - 27\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 15.243 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:03:56.385Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 3.436 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-03T18:03:56.420Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 3.217 ms - 188\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:03:56.456Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 6.182 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-03T18:03:56.502Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 2","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"ip":"************","level":"info","message":"API Request: GET /auth/users-by-department?_t=*************","service":"vms-server","timestamp":"2025-08-03T18:03:56.525Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 active users for login dropdown (GUEST accounts excluded)","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=************* \u001b[32m200\u001b[0m 6.189 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-03 18:03:56"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:02:59.712Z to 2025-08-03T18:03:59.715Z","service":"vms-server","timestamp":"2025-08-03 18:03:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:03:59.715Z to 2025-08-03T18:04:59.714Z","service":"vms-server","timestamp":"2025-08-03 18:04:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:05:43","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:05:43","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:04:59.714Z to 2025-08-03T18:05:59.718Z","service":"vms-server","timestamp":"2025-08-03 18:05:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:08","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:08","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:32","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:32","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:42","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:43","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:58","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:06:58","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:05:59.718Z to 2025-08-03T18:06:59.721Z","service":"vms-server","timestamp":"2025-08-03 18:06:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:07:13","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:07:13","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:07:24","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:07:24","uptime":"0h"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 18:07:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:06:59.721Z to 2025-08-03T18:07:59.721Z","service":"vms-server","timestamp":"2025-08-03 18:07:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:08:49","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:08:49","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:07:59.721Z to 2025-08-03T18:08:59.732Z","service":"vms-server","timestamp":"2025-08-03 18:08:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:08:59.732Z to 2025-08-03T18:09:59.738Z","service":"vms-server","timestamp":"2025-08-03 18:09:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:10:43","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:10:43","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:09:59.738Z to 2025-08-03T18:10:59.753Z","service":"vms-server","timestamp":"2025-08-03 18:10:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:08","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:08","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:32","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:32","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:42","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:43","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:58","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:11:58","uptime":"0h"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:10:59.753Z to 2025-08-03T18:11:59.754Z","service":"vms-server","timestamp":"2025-08-03 18:11:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:12:13","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:12:13","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:12:24","uptime":"0h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:12:24","uptime":"0h"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-03 18:12:59"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-03 18:12:59"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-03 18:12:59"}
{"level":"info","message":"✅ All users have unique sessions (0 unique active users)","service":"vms-server","timestamp":"2025-08-03 18:12:59"}
{"level":"info","message":"🕐 Live time sync: Updated system_time from 2025-08-03T18:11:59.754Z to 2025-08-03T18:12:59.763Z","service":"vms-server","timestamp":"2025-08-03 18:12:59"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"22MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:13:49","uptime":"1h"}
{"connections":"0/0 healthy","level":"info","locks":"0 total (0 expired, 0 orphaned)","memory":"23MB heap used","message":"🏥 System Health Check:","service":"vms-server","timestamp":"2025-08-03 18:13:49","uptime":"1h"}
