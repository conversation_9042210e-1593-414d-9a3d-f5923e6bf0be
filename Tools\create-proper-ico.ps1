# Create Proper ICO from VMS Logo
Write-Host "Creating proper Windows ICO file..." -ForegroundColor Green

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

try {
    # Load the original logo
    $logoPath = (Get-Item "vms logo.jpg").FullName
    $originalImage = [System.Drawing.Image]::FromFile($logoPath)
    
    Write-Host "Original image loaded: $($originalImage.Width)x$($originalImage.Height)" -ForegroundColor Cyan
    
    # Create a proper 32x32 icon (standard Windows size)
    $iconSize = 32
    $bitmap = New-Object System.Drawing.Bitmap($iconSize, $iconSize)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # High quality rendering
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    
    # Draw the resized image
    $graphics.DrawImage($originalImage, 0, 0, $iconSize, $iconSize)
    
    # Create icon handle
    $hIcon = $bitmap.GetHicon()
    $icon = [System.Drawing.Icon]::FromHandle($hIcon)
    
    # Backup old ICO
    if (Test-Path "vms-logo.ico") {
        Move-Item "vms-logo.ico" "vms-logo-backup.ico" -Force
        Write-Host "Backed up old ICO file" -ForegroundColor Yellow
    }
    
    # Save new ICO
    $fileStream = [System.IO.File]::Create("vms-logo.ico")
    $icon.Save($fileStream)
    $fileStream.Close()
    
    # Clean up
    $graphics.Dispose()
    $bitmap.Dispose()
    $originalImage.Dispose()
    $icon.Dispose()
    
    Write-Host "✅ New ICO file created successfully!" -ForegroundColor Green
    
    # Verify the new file
    if (Test-Path "vms-logo.ico") {
        $newIco = Get-Item "vms-logo.ico"
        Write-Host "📁 New ICO: $($newIco.Length) bytes" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Restore backup if creation failed
    if (Test-Path "vms-logo-backup.ico") {
        Move-Item "vms-logo-backup.ico" "vms-logo.ico" -Force
        Write-Host "Restored backup ICO file" -ForegroundColor Yellow
    }
}
