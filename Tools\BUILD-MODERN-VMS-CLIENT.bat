@echo off
title Building Modern VMS Client (.NET 9.0)
color 0A

echo ================================================================
echo                  BUILD MODERN VMS CLIENT
echo                    .NET 9.0 - PRODUCTION READY
echo                   SELF-CONTAINED DEPLOYMENT
echo ================================================================
echo.

echo [INFO] 🚀 Building modern VMS Client with .NET 9.0...
echo [INFO] 📦 Self-contained - NO .NET required on client PCs
echo.

REM Check if .NET 9.0 is available
echo [1/5] 🔍 Checking .NET 9.0 availability...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] ❌ .NET SDK not found!
    echo [INFO] Please install .NET 9.0 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo [SUCCESS] ✅ .NET SDK %DOTNET_VERSION% found
echo.

REM Clean previous builds
echo [2/5] 🧹 Cleaning previous builds...
if exist "bin" rmdir /s /q "bin" >nul 2>&1
if exist "obj" rmdir /s /q "obj" >nul 2>&1
if exist "VMS-Client.exe" del "VMS-Client.exe" >nul 2>&1
echo [SUCCESS] ✅ Build environment cleaned
echo.

REM Restore dependencies
echo [3/5] 📦 Restoring dependencies...
dotnet restore VMS-Client-Modern.csproj
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Failed to restore dependencies!
    pause
    exit /b 1
)
echo [SUCCESS] ✅ Dependencies restored
echo.

REM Build in Release mode
echo [4/5] 🏗️ Building in Release mode...
dotnet build VMS-Client-Modern.csproj -c Release --no-restore
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Build failed!
    pause
    exit /b 1
)
echo [SUCCESS] ✅ Build completed
echo.

REM Publish self-contained executable
echo [5/5] 📦 Publishing self-contained executable...
echo [INFO] This creates a single .exe file that works on any Windows PC
dotnet publish VMS-Client-Modern.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true -o publish
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Publish failed!
    pause
    exit /b 1
)

REM Move the executable to main directory
if exist "publish\VMS-Client.exe" (
    move "publish\VMS-Client.exe" "VMS-Client-Modern.exe" >nul
    echo [SUCCESS] ✅ Self-contained executable created
) else (
    echo [ERROR] ❌ Executable not found in publish directory!
    pause
    exit /b 1
)

REM Clean up publish directory
rmdir /s /q "publish" >nul 2>&1

echo.
echo ================================================================
echo                  BUILD COMPLETED SUCCESSFULLY
echo ================================================================
echo.

if exist "VMS-Client-Modern.exe" (
    echo ✅ SUCCESS: VMS-Client-Modern.exe has been created!
    echo.
    echo 📁 Location: %CD%\VMS-Client-Modern.exe
    echo 📏 Size: 
    for %%A in ("VMS-Client-Modern.exe") do echo    %%~zA bytes
    echo.
    echo 🚀 MODERN FEATURES:
    echo   ✅ .NET 9.0 - Latest and most reliable
    echo   ✅ Self-contained - NO .NET required on client PCs
    echo   ✅ Single executable file
    echo   ✅ Optimized and trimmed for size
    echo   ✅ Beautiful Windows GUI
    echo   ✅ Spinning loader animation
    echo   ✅ Professional user interface
    echo   ✅ No technical information shown
    echo   ✅ Automatic server discovery
    echo   ✅ Opens Chrome automatically
    echo   ✅ Production-ready reliability
    echo.
    echo 🎯 DEPLOYMENT ADVANTAGES:
    echo   ✅ Copy single .exe to any Windows computer
    echo   ✅ Works on Windows 10/11 without any installation
    echo   ✅ No .NET Framework dependency
    echo   ✅ No registry modifications needed
    echo   ✅ No admin rights required
    echo   ✅ Future-proof with latest .NET
    echo.
    echo 💡 USER EXPERIENCE:
    echo   - Clean, professional interface
    echo   - Spinning animation during connection
    echo   - "Please wait while we connect you to the VMS SYSTEM"
    echo   - No technical details or IP addresses shown
    echo   - Simple "Try Again" button if connection fails
    echo   - Automatic browser launch
    echo   - Self-closes after success
    echo.
    echo Press any key to test the modern client...
    pause >nul
    
    echo.
    echo [TESTING] 🧪 Running VMS-Client-Modern.exe...
    start "" "VMS-Client-Modern.exe"
    
) else (
    echo [ERROR] ❌ VMS-Client-Modern.exe was not created!
    echo [INFO] Check the error messages above for details.
)

echo.
echo Press any key to close...
pause >nul
