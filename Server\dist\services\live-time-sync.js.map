{"version": 3, "file": "live-time-sync.js", "sourceRoot": "", "sources": ["../../src/services/live-time-sync.ts"], "names": [], "mappings": ";;;AAAA,wDAAwD;AACxD,uCAAuC;AACvC,4CAAyC;AAEzC,MAAM,mBAAmB;IACf,YAAY,GAA0B,IAAI,CAAC;IAClC,gBAAgB,GAAG,KAAK,CAAC,CAAC,oBAAoB;IAE/D;;OAEG;IACI,KAAK;QACV,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO;QACT,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAE7D,eAAe;QACf,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,uBAAuB;QACvB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE1B,eAAM,CAAC,IAAI,CAAC,gDAAgD,IAAI,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACI,IAAI;QACT,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAA,UAAK,EAAC,uCAAuC,CAAU,CAAC;YAEjF,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACtD,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,WAAW,GAAG,eAAe,CAAC,aAAa,KAAK,KAAK,CAAC,CAAC,0BAA0B;YAEvF,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,oCAAoC;gBACpC,eAAM,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBACnE,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAE9E,yEAAyE;YACzE,IAAI,cAAc,GAAG,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAA,UAAK,EACT,yDAAyD,EACzD,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,CAChD,CAAC;gBAEF,eAAM,CAAC,IAAI,CAAC,+CAA+C,UAAU,CAAC,WAAW,EAAE,OAAO,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACzH,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC3E,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAE/B,MAAM,IAAA,UAAK,EACT,4EAA4E,EAC5E,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAClC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,yCAAyC,WAAW,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa;QACxB,IAAI,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAA,UAAK,EAAC,uCAAuC,CAAU,CAAC;YAEjF,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvC,OAAO,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC;YAC/C,CAAC;YAED,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACzD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9E,MAAM,WAAW,GAAG,eAAe,CAAC,aAAa,KAAK,KAAK,CAAC;YAE5D,OAAO;gBACL,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE;gBACtC,UAAU,EAAE,UAAU,CAAC,WAAW,EAAE;gBACpC,cAAc,EAAE,cAAc;gBAC9B,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;gBACzD,WAAW,EAAE,WAAW;gBACxB,UAAU,EAAE,WAAW;gBACvB,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI;gBAC5D,YAAY,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI;aAC3C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;QAC7E,CAAC;IACH,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC"}