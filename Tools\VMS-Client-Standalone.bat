@echo off
setlocal enabledelayedexpansion
title VMS Client - Connecting to Server...
color 0B

echo ================================================================
echo                    VMS CLIENT LAUNCHER
echo                 Automatic Server Discovery
echo                    NO NODE.JS REQUIRED
echo ================================================================
echo.
echo [INFO] 🔍 Searching for VMS Server on network...
echo [INFO] ⏳ Please wait while we connect you to VMS...
echo.

REM Set variables
set "CHROME_PATH="
set "VMS_URL="
set "SERVER_FOUND=0"

REM Find Chrome installation
echo [1/3] 🌐 Locating Chrome browser...
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Program Files
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Program Files (x86)
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Local AppData
) else if exist "%USERPROFILE%\AppData\Local\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=%USERPROFILE%\AppData\Local\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: User AppData
) else (
    echo [ERROR] ❌ Chrome browser not found!
    echo [INFO] Please install Google Chrome and try again.
    echo [INFO] Download from: https://www.google.com/chrome/
    echo.
    echo [FALLBACK] Trying to open with default browser...
    set "CHROME_PATH=start"
)

echo.
echo [2/3] 🔍 Discovering VMS Server...

REM Get current computer's network information
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1"') do (
    set "CURRENT_IP=%%a"
    call :trim CURRENT_IP
    if defined CURRENT_IP (
        for /f "tokens=1-3 delims=." %%b in ("!CURRENT_IP!") do (
            set "NETWORK=%%b.%%c.%%d"
        )
    )
)

echo [INFO] 📡 Current network: !NETWORK!.x
echo.

REM Test common VMS server addresses
set "TEST_ADDRESSES=localhost:8080 127.0.0.1:8080"

REM Add network range addresses
if defined NETWORK (
    set "TEST_ADDRESSES=!TEST_ADDRESSES! !NETWORK!.1:8080 !NETWORK!.100:8080 !NETWORK!.101:8080 !NETWORK!.102:8080 !NETWORK!.200:8080 !NETWORK!.250:8080"
)

REM Test each address
for %%u in (!TEST_ADDRESSES!) do (
    echo [TESTING] 🔗 Checking http://%%u...
    
    REM Use PowerShell to test HTTP connection (works without Node.js)
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%%u/health' -TimeoutSec 3 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    
    if !errorlevel! == 0 (
        echo [SUCCESS] ✅ VMS Server found at http://%%u
        set "VMS_URL=http://%%u"
        set "SERVER_FOUND=1"
        goto :server_found
    )
)

REM If no server found with PowerShell, try curl (if available)
if %SERVER_FOUND% == 0 (
    echo [INFO] 📡 Trying alternative discovery method...
    
    for %%u in (!TEST_ADDRESSES!) do (
        curl -s --connect-timeout 2 --max-time 3 "http://%%u/health" >nul 2>&1
        if !errorlevel! == 0 (
            echo [SUCCESS] ✅ VMS Server discovered at http://%%u
            set "VMS_URL=http://%%u"
            set "SERVER_FOUND=1"
            goto :server_found
        )
    )
)

REM If still no server found, try ping + manual check
if %SERVER_FOUND% == 0 (
    echo [INFO] 🔍 Scanning network for active servers...
    
    if defined NETWORK (
        for /L %%i in (1,1,254) do (
            set "TEST_IP=!NETWORK!.%%i"
            
            REM Quick ping test
            ping -n 1 -w 100 !TEST_IP! >nul 2>&1
            if !errorlevel! == 0 (
                echo [PING] 📍 Found active host: !TEST_IP!
                
                REM Test if it's running VMS
                powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://!TEST_IP!:8080/health' -TimeoutSec 2 -UseBasicParsing; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
                
                if !errorlevel! == 0 (
                    echo [SUCCESS] ✅ VMS Server found at http://!TEST_IP!:8080
                    set "VMS_URL=http://!TEST_IP!:8080"
                    set "SERVER_FOUND=1"
                    goto :server_found
                )
            )
        )
    )
)

REM If still no server found
if %SERVER_FOUND% == 0 (
    echo.
    echo [ERROR] ❌ VMS Server not found on network!
    echo.
    echo [TROUBLESHOOTING] 🔧 Please check:
    echo   1. VMS Server is running
    echo   2. You are connected to the same network
    echo   3. Firewall is not blocking connections
    echo   4. Server is accessible on port 8080
    echo.
    echo [NETWORK INFO] 📡 Your network details:
    if defined CURRENT_IP (
        echo   Your IP: !CURRENT_IP!
        echo   Network: !NETWORK!.x
    ) else (
        echo   Could not detect network information
    )
    echo.
    echo [MANUAL] 🖱️  You can manually open Chrome and try:
    echo   - http://localhost:8080 (if server is on this computer)
    echo   - http://[SERVER-IP]:8080 (replace [SERVER-IP] with actual IP)
    echo.
    pause
    exit /b 1
)

:server_found
echo.
echo [3/3] 🚀 Launching VMS in Chrome...
echo [INFO] 🌐 Opening: %VMS_URL%

REM Launch Chrome with VMS
if "%CHROME_PATH%"=="start" (
    echo [INFO] Using default browser...
    start "" "%VMS_URL%"
) else (
    echo [INFO] Using Chrome browser...
    start "" "%CHROME_PATH%" --new-window --start-maximized --disable-web-security --user-data-dir="%TEMP%\vms-chrome" "%VMS_URL%"
)

REM Wait a moment for browser to start
timeout /t 3 /nobreak >nul

echo.
echo ================================================================
echo                    VMS CLIENT CONNECTED
echo ================================================================
echo.
echo ✅ SUCCESS: VMS is now open in your browser
echo 🌐 Server: %VMS_URL%
echo 🖥️  Browser: Chrome (or default browser)
echo.
echo [INFO] You can now close this window and use VMS normally.
echo [INFO] If you need to reconnect, just run this VMS Client again.
echo.
echo Press any key to close this launcher...
pause >nul
exit /b 0

REM Helper function to trim whitespace
:trim
setlocal EnableDelayedExpansion
set "var=!%~1!"
for /f "tokens=* delims= " %%a in ("!var!") do set "var=%%a"
for /l %%a in (1,1,100) do if "!var:~-1!"==" " set "var=!var:~0,-1!"
endlocal & set "%~1=%var%"
goto :eof
