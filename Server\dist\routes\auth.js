"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRouter = void 0;
const express_1 = __importDefault(require("express"));
// Simple authentication without password hashing
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
const audit_service_js_1 = require("../services/audit-service.js");
exports.authRouter = express_1.default.Router();
// Simplified login route for internal LAN use
exports.authRouter.post('/login', async (req, res) => {
    try {
        const { department, username, password } = req.body;
        // Basic validation
        if (!department || !username || !password) {
            return res.status(400).json({
                error: 'Please enter department, username, and password'
            });
        }
        // Normalize inputs for case-insensitive comparison
        const normalizedDepartment = department.toUpperCase().trim();
        const normalizedUsername = username.toUpperCase().trim();
        // PASSWORD CHANGE FIX: Handle trailing spaces in database names
        const users = await (0, db_js_1.query)(`
      SELECT id, name, password, department, role, is_active, last_login
      FROM users
      WHERE UPPER(TRIM(name)) = ? AND UPPER(TRIM(department)) = ? AND is_active = 1
    `, [normalizedUsername, normalizedDepartment]);
        if (users.length === 0) {
            logger_js_1.logger.warn(`Login attempt failed - user not found: ${normalizedUsername} (${normalizedDepartment})`);
            return res.status(401).json({
                error: 'Username, department, or password incorrect. Please check and try again.'
            });
        }
        const user = users[0];
        // Direct password comparison (no hashing for internal LAN use)
        if (password !== user.password) {
            logger_js_1.logger.warn(`Login attempt failed - wrong password: ${user.name} (${user.department})`);
            return res.status(401).json({
                error: 'Username, department, or password incorrect. Please check and try again.'
            });
        }
        // Update last login timestamp
        await (0, db_js_1.query)('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);
        logger_js_1.logger.info(`✅ Successful login: ${user.name} (${user.department}) - Role: ${user.role}`);
        // Log successful login to audit trail
        try {
            await audit_service_js_1.AuditService.logLogin(user.id, user.name, user.department, req.ip || 'unknown', req.get('User-Agent') || 'unknown');
        }
        catch (auditError) {
            logger_js_1.logger.warn('Failed to log login audit:', auditError);
            // Don't fail login if audit logging fails
        }
        // PRODUCTION FIX: Allow multiple concurrent sessions per user
        // Only clean up very old sessions (7+ days) to prevent database bloat
        await (0, db_js_1.query)('DELETE FROM active_sessions WHERE is_active = FALSE AND session_end < DATE_SUB(NOW(), INTERVAL 7 DAY)');
        // PRODUCTION FIX: Always create new session for better reliability
        // Multiple sessions per user are allowed for production flexibility
        const sessionId = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO active_sessions (id, user_id, user_name, department, session_start, last_activity, is_active)
       VALUES (?, ?, ?, ?, NOW(), NOW(), TRUE)`, [sessionId, user.id, user.name, user.department]);
        logger_js_1.logger.info(`Created new session for user ${user.name} (session: ${sessionId})`);
        // Optional: Clean up only very old sessions (30+ days) for this user
        await (0, db_js_1.query)(`UPDATE active_sessions SET is_active = FALSE, session_end = NOW()
       WHERE user_id = ? AND is_active = TRUE AND session_start < DATE_SUB(NOW(), INTERVAL 30 DAY)`, [user.id]);
        // Set session cookie for browser (FIXED: Use consistent cookie name)
        res.cookie('vms_session_id', sessionId, {
            httpOnly: true,
            secure: false, // HTTP for LAN
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            sameSite: 'lax'
        });
        // Return user data with session ID for client storage
        return res.json({
            success: true,
            sessionId: sessionId, // Send session ID instead of JWT token
            user: {
                id: user.id,
                name: user.name,
                department: user.department,
                role: user.role,
                lastLogin: new Date().toISOString()
            },
            message: 'Login successful - Welcome to VMS'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Login system error:', error);
        return res.status(500).json({
            error: 'System error. Please try again or contact IT support.'
        });
    }
});
// Register route
exports.authRouter.post('/register', async (req, res) => {
    try {
        const { name, password, department } = req.body;
        // Validate input
        if (!name || !password || !department) {
            return res.status(400).json({ error: 'Name, password, and department are required' });
        }
        // Create a new pending registration
        const registrationId = (0, uuid_1.v4)();
        const normalizedName = name.toUpperCase();
        const normalizedDepartment = department.toUpperCase();
        // SIMPLIFIED: Store password directly without hashing
        console.log(`Storing password directly: ${password}`);
        // No need for verification since we're storing the password as-is
        // Log for debugging
        console.log(`Creating new registration: ${normalizedName} (${normalizedDepartment}) with ID: ${registrationId}`);
        await (0, db_js_1.query)('INSERT INTO pending_registrations (id, name, password, department, date_requested, status) VALUES (?, ?, ?, ?, NOW(), ?)', [registrationId, normalizedName, password, normalizedDepartment, 'pending']);
        // Create notification for admin users
        const notificationId = (0, uuid_1.v4)();
        await (0, db_js_1.query)('INSERT INTO notifications (id, user_id, message, is_read, timestamp, type) VALUES (?, ?, ?, ?, NOW(), ?)', [notificationId, 'admin', `New user registration: ${normalizedName} (${normalizedDepartment})`, false, 'OTHER']);
        // Create registration object for broadcasting
        const newRegistration = {
            id: registrationId,
            name: normalizedName,
            department: normalizedDepartment,
            dateRequested: new Date().toISOString(),
            status: 'pending'
        };
        // Broadcast registration update to all connected clients
        (0, socketHandlers_js_1.broadcastRegistrationUpdate)('created', newRegistration);
        // Log the broadcast
        console.log(`Broadcasting new registration: ${registrationId}`);
        // Verify the registration was added
        const verifyRegistrations = await (0, db_js_1.query)('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]);
        console.log(`Verification: Found ${verifyRegistrations.length} registrations with ID ${registrationId}`);
        res.status(201).json({
            message: 'Registration request submitted successfully',
            registrationId
        });
    }
    catch (error) {
        logger_js_1.logger.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});
// Get current user route
exports.authRouter.get('/me', auth_js_1.authenticate, async (req, res) => {
    try {
        const userId = req.user.id;
        // Get user from database
        const users = await (0, db_js_1.query)('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        const user = users[0];
        // Return user data with proper field mapping
        res.json({
            id: user.id,
            name: user.name,
            department: user.department,
            role: user.role,
            lastLogin: user.last_login,
            dateCreated: user.date_created,
            isActive: Boolean(user.is_active)
        });
    }
    catch (error) {
        logger_js_1.logger.error('Get current user error:', error);
        res.status(500).json({ error: 'Failed to get user data' });
    }
});
// Logout route - now handles session termination
exports.authRouter.post('/logout', auth_js_1.authenticate, async (req, res) => {
    try {
        // CRITICAL FIX: Get the session ID from the token
        const sessionId = req.user.sessionId;
        if (!sessionId) {
            logger_js_1.logger.warn(`User ${req.user.name} (${req.user.id}) attempted to logout without a valid session ID`);
            return res.status(400).json({ error: 'No active session found' });
        }
        // PRESENCE CLEANUP FIX: Force cleanup of user's resource locks on logout
        try {
            const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
            const releasedLocks = releaseUserLocksOnLogout(req.user.id);
            if (releasedLocks > 0) {
                logger_js_1.logger.info(`🧹 Logout cleanup: Released ${releasedLocks} resource locks for user ${req.user.name} (${req.user.id})`);
            }
        }
        catch (error) {
            logger_js_1.logger.warn('Failed to cleanup resource locks on logout:', error);
            // Don't fail logout if lock cleanup fails
        }
        // Mark the session as inactive
        await (0, db_js_1.query)('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE id = ?', [sessionId]);
        // Log logout to audit trail
        try {
            await audit_service_js_1.AuditService.logLogout(req.user.id, req.user.name, req.user.department, req.ip || 'unknown', req.get('User-Agent') || 'unknown');
        }
        catch (auditError) {
            logger_js_1.logger.warn('Failed to log logout audit:', auditError);
            // Don't fail logout if audit logging fails
        }
        logger_js_1.logger.info(`User ${req.user.name} (${req.user.id}) logged out successfully. Session ID: ${sessionId}`);
        res.json({ message: 'Logged out successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
});
// ENHANCED: Immediate logout endpoint for browser close detection
exports.authRouter.post('/immediate-logout', async (req, res) => {
    try {
        const { userId, sessionId, reason, timestamp } = req.body;
        // FIXED: Safe timestamp handling to prevent Invalid time value error
        let timeString = 'unknown';
        try {
            if (timestamp && !isNaN(timestamp)) {
                timeString = new Date(Number(timestamp)).toISOString();
            }
            else {
                timeString = new Date().toISOString();
            }
        }
        catch (timeError) {
            timeString = new Date().toISOString();
        }
        logger_js_1.logger.info(`🚪 IMMEDIATE LOGOUT: User ${userId} (${reason}) at ${timeString}`);
        if (sessionId && sessionId !== 'unknown') {
            // Check if session exists before deactivating
            const sessionCheck = await (0, db_js_1.query)('SELECT id, user_id, user_name FROM active_sessions WHERE id = ? AND is_active = TRUE', [sessionId]);
            if (sessionCheck.length > 0) {
                // Immediately deactivate session
                await (0, db_js_1.query)('UPDATE active_sessions SET is_active = FALSE, logout_reason = ?, session_end = NOW() WHERE id = ?', [reason, sessionId]);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: Session ${sessionId} found and deactivated`);
            }
            else {
                logger_js_1.logger.warn(`⚠️ IMMEDIATE LOGOUT: Session ${sessionId} not found or already inactive`);
            }
            // Clean up user connections and locks immediately (regardless of session status)
            try {
                const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
                const cleanedUp = await releaseUserLocksOnLogout(userId);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed - ${cleanedUp} locks released`);
            }
            catch (lockError) {
                logger_js_1.logger.warn('Failed to cleanup locks on immediate logout:', lockError);
            }
        }
        else {
            logger_js_1.logger.warn(`⚠️ IMMEDIATE LOGOUT: No valid sessionId provided for user ${userId}`);
            // Still try to cleanup user connections even without sessionId
            try {
                const { releaseUserLocksOnLogout } = require('../socket/socketHandlers');
                const cleanedUp = await releaseUserLocksOnLogout(userId);
                logger_js_1.logger.info(`✅ IMMEDIATE LOGOUT: User ${userId} cleanup completed without session - ${cleanedUp} locks released`);
            }
            catch (lockError) {
                logger_js_1.logger.warn('Failed to cleanup locks on immediate logout:', lockError);
            }
        }
        // Always respond quickly for sendBeacon
        res.status(200).json({ success: true });
    }
    catch (error) {
        logger_js_1.logger.error('❌ Immediate logout error:', error);
        // Still respond with success to avoid beacon retries
        res.status(200).json({ success: false, error: error instanceof Error ? error.message : String(error) });
    }
});
// Get users by department (public endpoint for login page)
exports.authRouter.get('/users-by-department', async (req, res) => {
    try {
        // PRODUCTION FIX: Exclude GUEST accounts and ensure only active users for login
        const users = await (0, db_js_1.query)(`
      SELECT id, name, department
      FROM users
      WHERE is_active = 1
        AND name NOT LIKE '%GUEST%'
        AND name NOT LIKE '%guest%'
        AND role NOT LIKE '%GUEST%'
      ORDER BY department, name
    `);
        logger_js_1.logger.info(`Fetched ${users.length} active users for login dropdown (GUEST accounts excluded)`);
        // Set cache control headers to prevent caching
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.json(users);
    }
    catch (error) {
        logger_js_1.logger.error('Get users by department error:', error);
        res.status(500).json({ error: 'Failed to get users' });
    }
});
// REMOVED: All duplicate endpoints that were causing authentication conflicts
// Only the proper authenticated endpoints above should be used
//# sourceMappingURL=auth.js.map