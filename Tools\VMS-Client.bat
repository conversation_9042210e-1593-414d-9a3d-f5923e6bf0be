@echo off
title VMS Client - Connecting to Server...
color 0B

echo ================================================================
echo                    VMS CLIENT LAUNCHER
echo                 Automatic Server Discovery
echo ================================================================
echo.
echo [INFO] 🔍 Searching for VMS Server on network...
echo [INFO] ⏳ Please wait while we connect you to VMS...
echo.

REM Set variables
set "CHROME_PATH="
set "VMS_URL="
set "SERVER_FOUND=0"

REM Find Chrome installation
echo [1/3] 🌐 Locating Chrome browser...
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Program Files
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Program Files (x86)
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set "CHROME_PATH=%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
    echo [SUCCESS] ✅ Chrome found: Local AppData
) else (
    echo [ERROR] ❌ Chrome browser not found!
    echo [INFO] Please install Google Chrome and try again.
    echo [INFO] Download from: https://www.google.com/chrome/
    pause
    exit /b 1
)

echo.
echo [2/3] 🔍 Discovering VMS Server...

REM Try common VMS server addresses
set "TEST_URLS=http://localhost:8080 http://127.0.0.1:8080"

REM Get current computer's IP and try network range
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1-3 delims=." %%b in ("%%a") do (
        set "NETWORK=%%b.%%c.%%d"
        call :trim NETWORK
    )
)

REM Add network range to test URLs
if defined NETWORK (
    set "TEST_URLS=%TEST_URLS% http://%NETWORK%.1:8080 http://%NETWORK%.100:8080 http://%NETWORK%.101:8080 http://%NETWORK%.102:8080 http://%NETWORK%.200:8080"
)

REM Test each URL
for %%u in (%TEST_URLS%) do (
    echo [TESTING] 🔗 Checking %%u...
    
    REM Use curl to test if server is responding
    curl -s --connect-timeout 2 --max-time 5 "%%u/health" >nul 2>&1
    if !errorlevel! == 0 (
        echo [SUCCESS] ✅ VMS Server found at %%u
        set "VMS_URL=%%u"
        set "SERVER_FOUND=1"
        goto :server_found
    )
)

REM If no server found, try UDP discovery (advanced)
if %SERVER_FOUND% == 0 (
    echo [INFO] 📡 Trying advanced network discovery...
    
    REM Try to discover via common network broadcast
    for /L %%i in (1,1,254) do (
        if defined NETWORK (
            set "TEST_IP=%NETWORK%.%%i"
            curl -s --connect-timeout 1 --max-time 2 "http://!TEST_IP!:8080/health" >nul 2>&1
            if !errorlevel! == 0 (
                echo [SUCCESS] ✅ VMS Server discovered at http://!TEST_IP!:8080
                set "VMS_URL=http://!TEST_IP!:8080"
                set "SERVER_FOUND=1"
                goto :server_found
            )
        )
    )
)

REM If still no server found
if %SERVER_FOUND% == 0 (
    echo.
    echo [ERROR] ❌ VMS Server not found on network!
    echo.
    echo [TROUBLESHOOTING] 🔧 Please check:
    echo   1. VMS Server is running
    echo   2. You are connected to the same network
    echo   3. Firewall is not blocking connections
    echo.
    echo [MANUAL] 🖱️  You can manually open Chrome and go to:
    echo   - http://localhost:8080 (if server is on this computer)
    echo   - http://[SERVER-IP]:8080 (replace [SERVER-IP] with actual IP)
    echo.
    pause
    exit /b 1
)

:server_found
echo.
echo [3/3] 🚀 Launching VMS in Chrome...
echo [INFO] 🌐 Opening: %VMS_URL%

REM Launch Chrome with VMS
start "" "%CHROME_PATH%" --new-window --start-maximized "%VMS_URL%"

REM Wait a moment for Chrome to start
timeout /t 3 /nobreak >nul

echo.
echo ================================================================
echo                    VMS CLIENT CONNECTED
echo ================================================================
echo.
echo ✅ SUCCESS: VMS is now open in Chrome browser
echo 🌐 Server: %VMS_URL%
echo 🖥️  Browser: Google Chrome
echo.
echo [INFO] You can now close this window and use VMS normally.
echo [INFO] If you need to reconnect, just run this VMS Client again.
echo.
echo Press any key to close this launcher...
pause >nul
exit /b 0

REM Helper function to trim whitespace
:trim
setlocal EnableDelayedExpansion
set "var=!%~1!"
for /f "tokens=* delims= " %%a in ("!var!") do set "var=%%a"
for /l %%a in (1,1,100) do if "!var:~-1!"==" " set "var=!var:~0,-1!"
endlocal & set "%~1=%var%"
goto :eof
